---
type: "always_apply"
---

# LIMS项目分析报告

## 项目概述

LIMS（Laboratory Information Management System）实验室信息管理系统是一个基于现代化技术栈构建的企业级应用系统。该项目采用前后端分离的架构设计，包含丰富的系统管理功能和业务模块。

### 核心功能

1. 用户管理：系统用户配置
2. 角色管理：角色菜单权限分配、数据范围权限划分
3. 菜单管理：系统菜单、操作权限、按钮权限标识配置
4. 部门管理：组织机构配置（公司、部门、小组）
5. 岗位管理：用户职务配置
6. 字典管理：固定数据维护
7. 参数管理：系统动态配置
8. 通知公告：信息发布维护
9. 操作日志：系统操作记录查询
10. 登录日志：系统登录记录查询
11. 在线用户：活跃用户状态监控
12. 定时任务：任务调度及执行结果日志
13. 服务监控：系统CPU、内存、磁盘等信息监控
14. 缓存监控：缓存信息查询和命令统计
15. 在线构建器：拖动表单元素生成HTML代码
16. 系统接口：自动生成API接口文档
17. 代码生成：一键生成前后端代码

## 技术架构

### 后端技术栈

- **框架**: FastAPI
- **数据库**: SQLAlchemy (异步模式)
- **数据库类型**: MySQL/PostgreSQL
- **缓存**: Redis
- **认证**: OAuth2 & JWT
- **其他依赖**:
  - APScheduler (定时任务)
  - pandas (数据处理)
  - openpyxl (Excel处理)
  - Pillow (图像处理)
  - reportlab (PDF生成)
  - python-docx (Word文档处理)

### 前端技术栈

- **框架**: Vue 3
- **UI库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite
- **其他依赖**:
  - Axios (HTTP客户端)
  - ECharts (数据可视化)
  - Ant Design Vue (UI组件)
  - js-cookie (Cookie操作)

## 项目结构

```
lims2/
├── back/                 # 后端代码
│   ├── config/           # 配置文件
│   ├── module_admin/     # 系统管理模块
│   ├── module_basedata/  # 基础数据模块
│   ├── module_customer/  # 客户管理模块
│   ├── module_generator/ # 代码生成模块
│   ├── module_quotation/ # 报价模块
│   ├── module_report/    # 报表模块
│   ├── module_sampling/  # 采样管理模块
│   ├── module_equipment_management/ # 设备管理模块
│   ├── module_bottle_maintenance/   # 瓶组管理模块
│   ├── tests/            # 单元测试
│   ├── utils/            # 工具类
│   ├── sql/              # 数据库脚本
│   ├── migrations/       # 数据库迁移文件
│   ├── logs/             # 日志文件
│   └── ...
├── front/                # 前端代码
│   ├── src/              # 源代码
│   │   ├── api/          # API接口
│   │   ├── assets/       # 静态资源
│   │   ├── components/   # 组件
│   │   ├── layout/       # 布局
│   │   ├── router/       # 路由
│   │   ├── store/        # 状态管理
│   │   ├── utils/        # 工具类
│   │   ├── views/        # 页面视图
│   │   └── ...
│   ├── public/           # 公共资源
│   └── ...
├── deploy/               # 部署相关
├── docs/                 # 文档
└── ...
```

## 环境配置

### 开发环境

- **后端**:
  - Python 3.x
  - 数据库: MySQL/PostgreSQL
  - Redis
  - 端口: 9099

- **前端**:
  - Node.js
  - 端口: 4000

### 配置文件

- 后端:
  - `.env.dev` (开发环境)
  - `.env.prod` (生产环境)

- 前端:
  - `.env.development` (开发环境)
  - `.env.production` (生产环境)
  - `.env.staging` (测试环境)

## 运行方式

### 后端运行

```bash
# 进入后端目录
cd back

# 安装依赖
pip install -e .

# 配置环境
# 在.env.dev文件中配置开发环境的数据库和redis

# 运行sql文件
# 1.新建数据库lims
# 2.如果使用的是MySQL数据库，使用命令或数据库连接工具运行sql文件夹下的init.sql；如果使用的是PostgreSQL数据库，使用命令或数据库连接工具运行sql文件夹下的init-pg.sql

# 运行后端
python run.py
```

### 前端运行

```bash
# 进入前端目录
cd front

# 安装依赖
npm install

# 启动服务
npm run dev
```

### 访问地址

- 默认账号密码: admin / admin123
- 浏览器访问地址: http://localhost:4000

## 开发规范

1. 前端采用Vue3、Element Plus，基于RuoYi-Vue3前端项目修改
2. 后端采用FastAPI、sqlalchemy[asyncio]、MySQL、Redis、OAuth2 & Jwt
3. 权限认证使用OAuth2 & Jwt，支持多终端认证系统
4. 支持加载动态权限菜单，多方式轻松权限控制
5. 前端变量命名为驼峰命名法
6. 后端变量命名为下划线命名法，数据库字段命名为下划线命名法
7. 所有表中的操作人、创建人、更新人都是系统用户表的外键
8. 数据库结构变更需要生成表变更sql文件，放在back/migrations目录下
9. 尽量生成单元测试，后端单元测试在back/tests目录下编写
10. 保证运行环境在back/venv虚拟环境中

## 部署方式

### 前端部署

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

### 后端部署

```bash
# 配置环境
# 在.env.prod文件中配置生产环境的数据库和redis

# 运行后端
python run.py --env=prod
```

## API文档

- 查看Swagger文档: http://localhost:4000/dev-api/docs

## 测试

```bash
# 运行测试
pytest
```