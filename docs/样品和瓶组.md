1. 采样任务执行列表（SamplingTaskAssignment），点击开始执行的时候，创建采样样品表和采样瓶组。
2. 项目报价明细的检测信息关联技术手册如果是现场直读类型的，需要排除，不用生成样品记录。
3. 对于相同采样任务、相同点位、相同样品类别、相同周期序号的采样周期条目，根据采样频次和样品数,生成频次*样品数的样品记录， 每条样品记录有样品序号，从1开始累加。
4. 样品数通过项目报价明细中的检测频率和样品数字段计算， 计算方式为频次*样品数。多报价明细合并时“样品数”的计算取最大值。
5. 每个样品需要分瓶组（采样瓶组表），根据任务周期条目关联项目报价明细的检测信息关联技术手册关联对应的瓶组信息，没有匹配到瓶组信息使用默认瓶组（关联的瓶组id为0表示默认瓶组）,同样瓶组只需要生成一条瓶组记录，需要注意的是默认瓶组单独生成记录，不聚合。
6. 瓶组需要生成瓶组编号，根据任务编号和瓶组序号生成。
7. 采样瓶组表有关联的样品、关联的瓶组信息、检测类别、检测参数等信息。因为有可能有多个检测方法、检测类别、检测参数，这部分作为关联检测参数表。
8. 在采样执行列表（/sampling/execution）的操作中增加样品管理和瓶组管理两个按钮，点击后查看和管理该任务的样品和瓶组， 管理界面弹窗显示。
