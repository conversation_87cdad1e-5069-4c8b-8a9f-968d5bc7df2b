<template>
  <el-dialog
    v-model="visible"
    title="瓶组管理"
    width="90%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="bottle-group-management">
      <!-- 工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="success"
            icon="Check"
            size="mini"
            :disabled="multiple"
            @click="handleBatchUse"
          >批量使用</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            icon="Finished"
            size="mini"
            :disabled="multiple"
            @click="handleBatchComplete"
          >批量完成</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            icon="Refresh"
            size="mini"
            @click="getList"
          >刷新</el-button>
        </el-col>
      </el-row>

      <!-- 瓶组列表 -->
      <el-table
        v-loading="loading"
        :data="bottleGroupList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="瓶组编号" align="center" prop="bottleGroupCode" width="150" />
        <el-table-column label="瓶组序号" align="center" prop="bottleGroupSequence" width="100" />
        <el-table-column label="周期信息" align="center" width="200">
          <template #default="scope">
            <div class="cycle-info">
              <p><strong>周期:</strong> {{ scope.row.cycleNumber }} ({{ scope.row.cycleType }})</p>
              <p><strong>类别:</strong> {{ scope.row.detectionCategory }}</p>
              <p><strong>点位:</strong> {{ scope.row.pointName }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="瓶组信息" align="center" width="200">
          <template #default="scope">
            <div v-if="scope.row.bottleInfo">
              <p><strong>编码:</strong> {{ scope.row.bottleInfo.bottleCode }}</p>
              <p><strong>类型:</strong> {{ scope.row.bottleInfo.bottleType }}</p>
              <p><strong>容量:</strong> {{ scope.row.bottleInfo.bottleVolume }}</p>
            </div>
            <span v-else>默认瓶组</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="statusOptions" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column label="检测参数" align="center" width="300">
          <template #default="scope">
            <div class="detection-params">
              <el-tag
                v-for="param in scope.row.detectionParams"
                :key="param.id"
                size="small"
                style="margin: 2px;"
              >
                {{ param.category }}-{{ param.parameter }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="描述" align="center" prop="bottleGroupDescription" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="text"
              icon="Edit"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              type="text"
              icon="Check"
              @click="handleUse(scope.row)"
              v-if="scope.row.status === 0"
            >使用</el-button>
            <el-button
              type="text"
              icon="Finished"
              @click="handleComplete(scope.row)"
              v-if="scope.row.status === 1"
            >完成</el-button>
            <el-button
              type="text"
              icon="View"
              @click="handleViewDetails(scope.row)"
            >详情</el-button>
            <el-button
              type="text"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-if="scope.row.status === 0"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 编辑瓶组对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑瓶组"
      width="600px"
      append-to-body
    >
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="120px">
        <el-form-item label="瓶组描述" prop="bottleGroupDescription">
          <el-input
            v-model="editForm.bottleGroupDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入瓶组描述"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="editForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitEdit">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 瓶组详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="瓶组详情"
      width="800px"
      append-to-body
    >
      <div v-if="currentBottleGroup">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="瓶组编号">{{ currentBottleGroup.bottleGroupCode }}</el-descriptions-item>
          <el-descriptions-item label="瓶组序号">{{ currentBottleGroup.bottleGroupSequence }}</el-descriptions-item>
          <el-descriptions-item label="周期序号">{{ currentBottleGroup.cycleNumber }}</el-descriptions-item>
          <el-descriptions-item label="周期类型">{{ currentBottleGroup.cycleType }}</el-descriptions-item>
          <el-descriptions-item label="检测类别">{{ currentBottleGroup.detectionCategory }}</el-descriptions-item>
          <el-descriptions-item label="点位名称">{{ currentBottleGroup.pointName }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="statusOptions" :value="currentBottleGroup.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="瓶组描述" :span="2">{{ currentBottleGroup.bottleGroupDescription || '-' }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentBottleGroup.remark || '-' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 瓶组信息 -->
        <el-divider content-position="left">瓶组信息</el-divider>
        <el-descriptions v-if="currentBottleGroup.bottleInfo" :column="2" border>
          <el-descriptions-item label="瓶组编码">{{ currentBottleGroup.bottleInfo.bottleCode }}</el-descriptions-item>
          <el-descriptions-item label="容器类型">{{ currentBottleGroup.bottleInfo.bottleType }}</el-descriptions-item>
          <el-descriptions-item label="容器容量">{{ currentBottleGroup.bottleInfo.bottleVolume }}</el-descriptions-item>
          <el-descriptions-item label="存储方式">
            <el-tag v-for="style in currentBottleGroup.bottleInfo.storageStyles" :key="style" size="small" style="margin-right: 5px;">
              {{ style }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="固定方式">
            <el-tag v-for="style in currentBottleGroup.bottleInfo.fixStyles" :key="style" size="small" style="margin-right: 5px;">
              {{ style }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="样品时效">
            {{ currentBottleGroup.bottleInfo.sampleAge }}{{ currentBottleGroup.bottleInfo.sampleAgeUnit }}
          </el-descriptions-item>
        </el-descriptions>
        <div v-else>
          <p>默认瓶组</p>
        </div>

        <!-- 检测参数 -->
        <el-divider content-position="left">检测参数</el-divider>
        <el-table :data="currentBottleGroup.detectionParams" border>
          <el-table-column label="检测类别" prop="category" />
          <el-table-column label="检测参数" prop="parameter" />
          <el-table-column label="检测方法" prop="method" />
          <el-table-column label="样品来源" prop="sampleSource" />
          <el-table-column label="点位数" prop="pointCount" />
          <el-table-column label="频次数" prop="frequency" />
          <el-table-column label="样品数" prop="sampleCount" />
        </el-table>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getBottleGroupsByAssignmentId,
  updateBottleGroup,
  deleteBottleGroup,
  batchUpdateBottleGroupStatus,
  getBottleGroupStatusOptions
} from '@/api/sampling/bottleGroup'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  assignmentId: {
    type: Number,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const bottleGroupList = ref([])
const statusOptions = ref([])
const selectedBottleGroups = ref([])
const multiple = computed(() => selectedBottleGroups.value.length !== 1)

// 编辑对话框
const editDialogVisible = ref(false)
const editForm = reactive({
  id: null,
  bottleGroupDescription: '',
  remark: ''
})

const editRules = {}

// 详情对话框
const detailDialogVisible = ref(false)
const currentBottleGroup = ref(null)

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal && props.assignmentId) {
    getList()
    getStatusOptions()
  }
})

// 获取瓶组列表
const getList = async () => {
  if (!props.assignmentId) return
  
  loading.value = true
  try {
    const response = await getBottleGroupsByAssignmentId(props.assignmentId)
    bottleGroupList.value = response.data || []
  } catch (error) {
    console.error('获取瓶组列表失败:', error)
    ElMessage.error('获取瓶组列表失败')
  } finally {
    loading.value = false
  }
}

// 获取状态选项
const getStatusOptions = async () => {
  try {
    const response = await getBottleGroupStatusOptions()
    statusOptions.value = response.data || []
  } catch (error) {
    console.error('获取状态选项失败:', error)
  }
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedBottleGroups.value = selection
}

// 编辑瓶组
const handleEdit = (row) => {
  editForm.id = row.id
  editForm.bottleGroupDescription = row.bottleGroupDescription || ''
  editForm.remark = row.remark || ''
  editDialogVisible.value = true
}

// 提交编辑
const submitEdit = async () => {
  try {
    const updateData = {
      bottleGroupDescription: editForm.bottleGroupDescription,
      remark: editForm.remark
    }
    
    await updateBottleGroup(editForm.id, updateData)
    ElMessage.success('更新成功')
    editDialogVisible.value = false
    getList()
  } catch (error) {
    console.error('更新瓶组失败:', error)
    ElMessage.error('更新瓶组失败')
  }
}

// 查看详情
const handleViewDetails = (row) => {
  currentBottleGroup.value = row
  detailDialogVisible.value = true
}

// 使用瓶组
const handleUse = async (row) => {
  try {
    await ElMessageBox.confirm('确认使用该瓶组？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await batchUpdateBottleGroupStatus([row.id], 1)
    ElMessage.success('使用成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('使用瓶组失败:', error)
      ElMessage.error('使用瓶组失败')
    }
  }
}

// 完成瓶组
const handleComplete = async (row) => {
  try {
    await ElMessageBox.confirm('确认完成该瓶组？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await batchUpdateBottleGroupStatus([row.id], 2)
    ElMessage.success('完成成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成瓶组失败:', error)
      ElMessage.error('完成瓶组失败')
    }
  }
}

// 删除瓶组
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该瓶组？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteBottleGroup(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除瓶组失败:', error)
      ElMessage.error('删除瓶组失败')
    }
  }
}

// 批量使用
const handleBatchUse = async () => {
  if (selectedBottleGroups.value.length === 0) {
    ElMessage.warning('请选择要使用的瓶组')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确认使用选中的 ${selectedBottleGroups.value.length} 个瓶组？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const bottleGroupIds = selectedBottleGroups.value.map(item => item.id)
    await batchUpdateBottleGroupStatus(bottleGroupIds, 1)
    ElMessage.success('批量使用成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量使用失败:', error)
      ElMessage.error('批量使用失败')
    }
  }
}

// 批量完成
const handleBatchComplete = async () => {
  if (selectedBottleGroups.value.length === 0) {
    ElMessage.warning('请选择要完成的瓶组')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确认完成选中的 ${selectedBottleGroups.value.length} 个瓶组？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const bottleGroupIds = selectedBottleGroups.value.map(item => item.id)
    await batchUpdateBottleGroupStatus(bottleGroupIds, 2)
    ElMessage.success('批量完成成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量完成失败:', error)
      ElMessage.error('批量完成失败')
    }
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.bottle-group-management {
  padding: 10px;
}

.cycle-info p {
  margin: 2px 0;
  font-size: 12px;
}

.detection-params {
  max-height: 100px;
  overflow-y: auto;
}

.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

.fixed-width .el-button {
  margin-right: 8px;
  margin-bottom: 4px;
}
</style>
