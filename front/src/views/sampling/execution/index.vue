<template>
  <div class="app-container">
    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="execution-tabs">
      <el-tab-pane label="我的执行任务" name="my-tasks"></el-tab-pane>
      <el-tab-pane label="所有执行任务" name="all-tasks" v-if="isAdmin"></el-tab-pane>
    </el-tabs>

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 200px">
          <el-option
            v-for="dict in dict.type.sampling_task_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="executionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务编号" align="center" prop="taskCode" />
      <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
      <el-table-column label="分组信息" align="center" width="200">
        <template #default="scope">
          <div class="group-info">
            <p><strong>周期:</strong> {{ scope.row.cycleNumber }} ({{ scope.row.cycleType }})</p>
            <p><strong>类别:</strong> {{ scope.row.detectionCategory }}</p>
            <p><strong>点位:</strong> {{ scope.row.pointName }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="执行人" align="center" prop="assignedUserNames" width="150">
        <template #default="scope">
          <el-tag v-for="name in scope.row.assignedUserNames" :key="name" size="small" style="margin: 2px;">
            {{ name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="dict.type.sampling_task_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="计划开始日期" align="center" prop="plannedStartDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.plannedStartDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划结束日期" align="center" prop="plannedEndDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.plannedEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            type="text"
            icon="VideoPlay"
            @click="handleExecute(scope.row)"
            v-if="scope.row.status === 0"
          >开始执行</el-button>
          <el-button
            type="text"
            icon="Check"
            @click="handleComplete(scope.row)"
            v-if="scope.row.status === 1"
          >完成任务</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" v-model="detailOpen" width="1200px" append-to-body class="detail-dialog">
      <div class="detail-content">
        <el-descriptions class="detail-descriptions" :column="3" border>
          <el-descriptions-item label="任务编号">{{ taskDetail.taskCode }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetail.taskName }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <dict-tag :options="dict.type.sampling_task_status" :value="taskDetail.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ taskDetail.projectName }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ taskDetail.customerName }}</el-descriptions-item>
          <el-descriptions-item label="责任人">{{ taskDetail.responsibleUserName }}</el-descriptions-item>
          <el-descriptions-item label="计划开始日期">{{ parseTime(taskDetail.plannedStartDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="计划结束日期">{{ parseTime(taskDetail.plannedEndDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="实际开始日期">{{ parseTime(taskDetail.actualStartDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="实际结束日期">{{ parseTime(taskDetail.actualEndDate, '{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(taskDetail.createTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="3">{{ taskDetail.description }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 项目周期条目 -->
        <div v-if="taskDetail.cycleItems && taskDetail.cycleItems.length > 0" style="margin-top: 20px;">
          <h3 style="margin-bottom: 16px; color: #303133;">项目周期条目</h3>
          <el-table :data="taskDetail.cycleItems" border style="width: 100%">
            <el-table-column prop="cycleNumber" label="周期序号" width="100" align="center" />
            <el-table-column prop="detectionClassification" label="检测分类" width="120" align="center" />
            <el-table-column prop="detectionCategory" label="检测类别" width="120" align="center" />
            <el-table-column prop="detectionParameter" label="检测参数" min-width="150" show-overflow-tooltip />
            <el-table-column prop="detectionMethod" label="检测方法" min-width="150" show-overflow-tooltip />
            <el-table-column prop="pointName" label="点位名称" width="120" align="center" />
            <el-table-column prop="sampleSource" label="样品来源" width="120" align="center" />
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getCycleItemStatusType(scope.row.status)" size="small">
                  {{ getCycleItemStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue';
import { getUserExecutorAssignments, getAllExecutorAssignments, getExecutorAssignmentDetail } from "@/api/sampling/executorAssignment";
import { getSamplingTask, updateSamplingTask } from "@/api/sampling/samplingTask";
import { checkPermi } from "@/utils/permission";
import { useDict } from "@/utils/dict";
import useUserStore from '@/store/modules/user';

// 定义组件名称
defineOptions({
  name: "SamplingExecution"
});

// 获取当前实例
const { proxy } = getCurrentInstance();

// 字典数据
const { sampling_task_status } = useDict('sampling_task_status');
const dict = reactive({
  type: {
    sampling_task_status
  }
});

// 响应式数据
const activeTab = ref('my-tasks');
const isAdmin = ref(false);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const executionList = ref([]);
const title = ref("");
const open = ref(false);
const detailOpen = ref(false);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  taskName: null,
  status: null
});
const taskDetail = ref({});

// 用户store
const userStore = useUserStore();

// 计算属性
const currentUserId = computed(() => {
  return userStore.id;
});

// 方法定义
/** 检查权限 */
const checkPermissions = () => {
  // 检查用户是否具有assignment-execution:all权限
  isAdmin.value = checkPermi(['assignment-execution:all']);
};



/** Tab切换处理 */
const handleTabClick = (tab) => {
  activeTab.value = tab.props?.name || tab.paneName;
  getList();
};

/** 查询执行任务列表 */
const getList = () => {
  loading.value = true;

  if (activeTab.value === 'my-tasks') {
    getMyTasks();
  } else if (activeTab.value === 'all-tasks') {
    getAllTasks();
  }
};

/** 获取我的执行任务 */
const getMyTasks = () => {
  const userId = userStore.id;

  // 检查用户ID是否存在
  if (!userId) {
    console.error('用户ID不存在，请先登录');
    proxy.$modal.msgError('用户信息获取失败，请重新登录');
    loading.value = false;
    return;
  }

  getUserExecutorAssignments(userId).then(response => {
    executionList.value = response.data || [];
    total.value = executionList.value.length;
    loading.value = false;
  }).catch(error => {
    console.error('获取我的执行任务列表失败:', error);
    proxy.$modal.msgError('获取任务列表失败');
    executionList.value = [];
    total.value = 0;
    loading.value = false;
  });
};

/** 获取所有执行任务（需要权限） */
const getAllTasks = () => {
  if (!isAdmin.value) {
    proxy.$modal.msgError('权限不足，无法查看所有执行任务');
    loading.value = false;
    return;
  }

  getAllExecutorAssignments().then(response => {
    executionList.value = response.data || [];
    total.value = executionList.value.length;
    loading.value = false;
  }).catch(error => {
    console.error('获取所有执行任务列表失败:', error);
    proxy.$modal.msgError('获取任务列表失败');
    executionList.value = [];
    total.value = 0;
    loading.value = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 详情按钮操作 */
const handleDetail = (row) => {
  const assignmentId = row.id;
  getExecutorAssignmentDetail(assignmentId).then(response => {
    taskDetail.value = response.data;
    detailOpen.value = true;
  });
};

/** 获取周期条目状态类型 */
const getCycleItemStatusType = (status) => {
  const statusMap = {
    0: 'warning',  // 未分配
    1: 'info',     // 已分配
    2: 'primary',  // 执行中
    3: 'success'   // 已完成
  };
  return statusMap[status] || 'info';
};

/** 获取周期条目状态标签 */
const getCycleItemStatusLabel = (status) => {
  const statusMap = {
    0: '未分配',
    1: '已分配',
    2: '执行中',
    3: '已完成'
  };
  return statusMap[status] || '未知状态';
};

/** 开始执行按钮操作 */
const handleExecute = (row) => {
  proxy.$modal.confirm('确认开始执行该任务？').then(() => {
    const updateData = {
      id: row.samplingTaskId,
      status: 1, // 执行中
      actualStartDate: new Date().toISOString().split('T')[0]
    };
    
    updateSamplingTask(updateData).then(response => {
      proxy.$modal.msgSuccess("任务已开始执行");
      getList();
    });
  }).catch(() => {});
};

/** 完成任务按钮操作 */
const handleComplete = (row) => {
  proxy.$modal.confirm('确认完成该任务？').then(() => {
    const updateData = {
      id: row.samplingTaskId,
      status: 2, // 已完成
      actualEndDate: new Date().toISOString().split('T')[0]
    };
    
    updateSamplingTask(updateData).then(response => {
      proxy.$modal.msgSuccess("任务已完成");
      getList();
    });
  }).catch(() => {});
};



// 组件挂载时执行
 onMounted(() => {
   checkPermissions();
   getList();
 });
</script>

<style scoped>
.app-container {
  padding: 20px;
}



/* 按钮组样式优化 */
.el-row.mb8 .el-button {
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-row.mb8 .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 表格操作列样式 */
.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

.fixed-width .el-button {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: #f0f9ff !important;
}

/* 状态标签样式增强 */
.dict-tag {
  font-weight: 500;
  border-radius: 12px;
  padding: 4px 8px;
}

/* Tab样式 */
.execution-tabs {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 16px;
}

.execution-tabs .el-tabs__header {
  margin-bottom: 0;
  border-bottom: 2px solid #e9ecef;
}

.execution-tabs .el-tabs__nav-wrap::after {
  height: 2px;
  background-color: #e9ecef;
}

.execution-tabs .el-tabs__active-bar {
  background-color: #409eff;
}

.execution-tabs .el-tabs__item {
  font-weight: 500;
  color: #606266;
  transition: all 0.3s ease;
}

.execution-tabs .el-tabs__item:hover {
  color: #409eff;
}

.execution-tabs .el-tabs__item.is-active {
  color: #409eff;
  font-weight: 600;
}

/* 对话框样式优化 */
.el-dialog__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.el-dialog__title {
  font-weight: 600;
  color: #303133;
}

.detail-dialog .el-dialog__body {
  padding: 20px;
  height: 80vh;
  overflow: hidden;
}

.detail-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-descriptions {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: 600;
  color: #606266;
}

/* 分组信息样式 */
.group-info {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  margin-bottom: 16px;
}

.group-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

/* 周期项目表格样式 */
.cycle-items-section {
  margin-top: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-title {
  margin: 0 0 16px 0;
  color: #303133;
  font-weight: 600;
  font-size: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
  flex-shrink: 0;
}

.cycle-items-table {
  border-radius: 6px;
  overflow: hidden;
  flex: 1;
}

.cycle-items-table .el-table__body-wrapper {
  max-height: calc(80vh - 300px);
  overflow-y: auto;
}

/* 卡片样式增强 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.el-card__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .search-form {
    padding: 12px;
  }
  
  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .el-form-item .el-input,
  .el-form-item .el-select {
    width: 100% !important;
  }
  
  .el-table {
    font-size: 12px;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .execution-tabs {
    padding: 0 8px;
  }
  
  .el-table-column {
    min-width: 80px;
  }
}
</style>
