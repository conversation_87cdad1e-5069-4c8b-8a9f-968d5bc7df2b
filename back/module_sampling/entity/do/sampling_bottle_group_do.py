"""
采样瓶组数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, Text, ForeignKey, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from config.database import Base


class SamplingBottleGroup(Base):
    """采样瓶组表"""
    __tablename__ = 'sampling_bottle_group'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    task_assignment_id = Column(BigInteger, ForeignKey('sampling_task_executor_assignment.id'), nullable=False, comment='任务执行指派ID')
    bottle_maintenance_id = Column(Integer, ForeignKey('bottle_maintenance.id'), nullable=True, comment='瓶组管理ID（0表示默认瓶组）')
    
    # 瓶组信息
    bottle_group_code = Column(String(100), nullable=False, comment='瓶组编号（根据任务编号和瓶组序号生成）')
    bottle_group_sequence = Column(Integer, nullable=False, comment='瓶组序号（在同一任务中的序号）')
    
    # 周期信息（来自任务执行指派）
    cycle_number = Column(Integer, nullable=False, comment='周期序号')
    cycle_type = Column(String(50), comment='周期类型')
    detection_category = Column(String(100), comment='检测类别')
    point_name = Column(String(200), comment='点位名称')
    
    # 瓶组状态
    status = Column(Integer, default=0, comment='瓶组状态：0-待使用，1-使用中，2-已完成')
    
    # 瓶组描述
    bottle_group_description = Column(Text, comment='瓶组描述')
    remark = Column(Text, comment='备注')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义
    sampling_task = relationship("SamplingTask", back_populates="bottle_groups")
    task_assignment = relationship("SamplingTaskAssignment", back_populates="bottle_groups")
    bottle_maintenance = relationship("BottleMaintenance")
    detection_params = relationship("SamplingBottleGroupDetectionParam", back_populates="bottle_group", cascade="all, delete-orphan")

    # 创建人、更新人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引
    __table_args__ = (
        Index('idx_sampling_bottle_group_task_id', 'sampling_task_id'),
        Index('idx_sampling_bottle_group_assignment_id', 'task_assignment_id'),
        Index('idx_sampling_bottle_group_bottle_id', 'bottle_maintenance_id'),
        Index('idx_sampling_bottle_group_cycle', 'cycle_number', 'cycle_type', 'detection_category', 'point_name'),
        Index('idx_sampling_bottle_group_code', 'bottle_group_code'),
        {'comment': '采样瓶组表'}
    )
    
    def __repr__(self):
        return f"<SamplingBottleGroup(id={self.id}, code={self.bottle_group_code}, task_id={self.sampling_task_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sampling_task_id': self.sampling_task_id,
            'task_assignment_id': self.task_assignment_id,
            'bottle_maintenance_id': self.bottle_maintenance_id,
            'bottle_group_code': self.bottle_group_code,
            'bottle_group_sequence': self.bottle_group_sequence,
            'cycle_number': self.cycle_number,
            'cycle_type': self.cycle_type,
            'detection_category': self.detection_category,
            'point_name': self.point_name,
            'status': self.status,
            'bottle_group_description': self.bottle_group_description,
            'remark': self.remark,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
