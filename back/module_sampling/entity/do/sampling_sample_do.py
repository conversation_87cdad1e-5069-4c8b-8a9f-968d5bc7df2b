"""
采样样品数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, Text, ForeignKey, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from config.database import Base


class SamplingSample(Base):
    """采样样品表"""
    __tablename__ = 'sampling_sample'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    task_assignment_id = Column(BigInteger, ForeignKey('sampling_task_executor_assignment.id'), nullable=False, comment='任务执行指派ID')
    
    # 样品信息
    sample_number = Column(Integer, nullable=False, comment='样品序号（从1开始累加）')
    sample_code = Column(String(100), nullable=True, comment='样品编号（可选，用于标识具体样品）')
    
    # 周期信息（来自任务执行指派）
    cycle_number = Column(Integer, nullable=False, comment='周期序号')
    cycle_type = Column(String(50), comment='周期类型')
    detection_category = Column(String(100), comment='检测类别')
    point_name = Column(String(200), comment='点位名称')
    
    # 样品状态
    status = Column(Integer, default=0, comment='样品状态：0-待采集，1-已采集，2-已送检，3-检测中，4-已完成')
    
    # 采集信息
    collection_date = Column(DateTime, comment='采集日期')
    collection_time = Column(DateTime, comment='采集时间')
    collector_id = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='采集人ID')
    collection_location = Column(String(500), comment='采集地点详细描述')
    
    # 样品描述
    sample_description = Column(Text, comment='样品描述')
    remark = Column(Text, comment='备注')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义
    sampling_task = relationship("SamplingTask", back_populates="samples")
    task_assignment = relationship("SamplingTaskAssignment", back_populates="samples")
    
    # 创建人、更新人、采集人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    updater = relationship("SysUser", foreign_keys=[update_by])
    collector = relationship("SysUser", foreign_keys=[collector_id])
    
    # 索引
    __table_args__ = (
        Index('idx_sampling_sample_task_id', 'sampling_task_id'),
        Index('idx_sampling_sample_assignment_id', 'task_assignment_id'),
        Index('idx_sampling_sample_cycle', 'cycle_number', 'cycle_type', 'detection_category', 'point_name'),
        Index('idx_sampling_sample_status', 'status'),
        {'comment': '采样样品表'}
    )
    
    def __repr__(self):
        return f"<SamplingSample(id={self.id}, sample_number={self.sample_number}, task_id={self.sampling_task_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sampling_task_id': self.sampling_task_id,
            'task_assignment_id': self.task_assignment_id,
            'sample_number': self.sample_number,
            'sample_code': self.sample_code,
            'cycle_number': self.cycle_number,
            'cycle_type': self.cycle_type,
            'detection_category': self.detection_category,
            'point_name': self.point_name,
            'status': self.status,
            'collection_date': self.collection_date.isoformat() if self.collection_date else None,
            'collection_time': self.collection_time.isoformat() if self.collection_time else None,
            'collector_id': self.collector_id,
            'collection_location': self.collection_location,
            'sample_description': self.sample_description,
            'remark': self.remark,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
