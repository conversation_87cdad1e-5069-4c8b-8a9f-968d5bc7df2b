"""
采样瓶组检测参数关联表数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, ForeignKey, Index
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from config.database import Base


class SamplingBottleGroupDetectionParam(Base):
    """采样瓶组检测参数关联表"""
    __tablename__ = 'sampling_bottle_group_detection_param'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    bottle_group_id = Column(BigInteger, ForeignKey('sampling_bottle_group.id'), nullable=False, comment='采样瓶组ID')
    project_quotation_item_id = Column(BigInteger, ForeignKey('project_quotation_item.id'), nullable=False, comment='项目报价明细ID')
    technical_manual_id = Column(BigInteger, ForeignKey('technical_manual.id'), nullable=True, comment='技术手册ID')
    
    # 检测信息（冗余存储，便于查询）
    qualification_code = Column(String(50), nullable=True, comment='资质唯一编号')
    classification = Column(String(50), nullable=True, comment='分类')
    category = Column(String(50), nullable=False, comment='检测类别(二级分类)')
    parameter = Column(String(100), nullable=False, comment='检测参数(指标)')
    method = Column(String(200), nullable=False, comment='检测方法')
    limitation_scope = Column(String(200), nullable=True, comment='限制范围')
    
    # 采样信息（来自项目报价明细）
    sample_source = Column(String(50), nullable=True, comment='样品来源')
    point_name = Column(String(100), nullable=True, comment='点位名称')
    point_count = Column(Integer, nullable=False, default=1, comment='点位数')
    cycle_type = Column(String(10), nullable=True, comment='检测周期类型')
    cycle_count = Column(Integer, nullable=True, default=1, comment='检测周期数')
    frequency = Column(Integer, nullable=True, default=1, comment='检测频次数')
    sample_count = Column(Integer, nullable=True, default=1, comment='样品数')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义
    bottle_group = relationship("SamplingBottleGroup", back_populates="detection_params")
    project_quotation_item = relationship("ProjectQuotationItem")
    technical_manual = relationship("TechnicalManual")
    
    # 创建人、更新人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引
    __table_args__ = (
        Index('idx_bottle_group_detection_bottle_id', 'bottle_group_id'),
        Index('idx_bottle_group_detection_item_id', 'project_quotation_item_id'),
        Index('idx_bottle_group_detection_manual_id', 'technical_manual_id'),
        Index('idx_bottle_group_detection_category', 'category', 'parameter', 'method'),
        {'comment': '采样瓶组检测参数关联表'}
    )
    
    def __repr__(self):
        return f"<SamplingBottleGroupDetectionParam(id={self.id}, bottle_group_id={self.bottle_group_id}, parameter={self.parameter})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'bottle_group_id': self.bottle_group_id,
            'project_quotation_item_id': self.project_quotation_item_id,
            'technical_manual_id': self.technical_manual_id,
            'qualification_code': self.qualification_code,
            'classification': self.classification,
            'category': self.category,
            'parameter': self.parameter,
            'method': self.method,
            'limitation_scope': self.limitation_scope,
            'sample_source': self.sample_source,
            'point_name': self.point_name,
            'point_count': self.point_count,
            'cycle_type': self.cycle_type,
            'cycle_count': self.cycle_count,
            'frequency': self.frequency,
            'sample_count': self.sample_count,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
