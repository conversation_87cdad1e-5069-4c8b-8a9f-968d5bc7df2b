"""
采样任务执行人指派数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SamplingTaskAssignment(Base):
    """采样任务执行人指派表"""
    __tablename__ = 'sampling_task_executor_assignment'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    
    # 聚合字段（用于分组）
    cycle_number = Column(Integer, nullable=False, comment='周期序号')
    cycle_type = Column(String(50), comment='周期类型')
    detection_category = Column(String(100), comment='检测类别')
    point_name = Column(String(200), comment='点位名称')
    
    # 分配信息
    assigned_user_ids = Column(Text, comment='分配的执行人ID列表（JSON格式）')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义
    sampling_task = relationship("SamplingTask", back_populates="task_assignments")
    samples = relationship("SamplingSample", back_populates="task_assignment", cascade="all, delete-orphan")
    bottle_groups = relationship("SamplingBottleGroup", back_populates="task_assignment", cascade="all, delete-orphan")

    # 创建人和更新人关系
    creator = relationship("SysUser", foreign_keys=[create_by])
    updater = relationship("SysUser", foreign_keys=[update_by])
    
    # 索引
    __table_args__ = (
        Index('idx_sampling_task_assignment_task_id', 'sampling_task_id'),
        Index('idx_sampling_task_assignment_cycle', 'cycle_number', 'cycle_type', 'detection_category', 'point_name'),
        {'comment': '采样任务指派表'}
    )
    
    def __repr__(self):
        return f"<SamplingTaskAssignment(id={self.id}, sampling_task_id={self.sampling_task_id}, cycle_number={self.cycle_number})>"
    
    def to_dict(self):
        """转换为字典"""
        import json
        assigned_users = []
        if self.assigned_user_ids:
            try:
                assigned_users = json.loads(self.assigned_user_ids)
            except:
                assigned_users = []
                
        return {
            'id': self.id,
            'sampling_task_id': self.sampling_task_id,
            'cycle_number': self.cycle_number,
            'cycle_type': self.cycle_type,
            'detection_category': self.detection_category,
            'point_name': self.point_name,
            'assigned_user_ids': assigned_users,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
