"""
采样瓶组DAO
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, update, and_
from sqlalchemy.orm import selectinload

from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_sampling.entity.do.sampling_bottle_group_detection_param_do import SamplingBottleGroupDetectionParam


class SamplingBottleGroupDAO:
    """采样瓶组DAO"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_bottle_group(self, bottle_group: SamplingBottleGroup) -> SamplingBottleGroup:
        """创建瓶组"""
        self.db.add(bottle_group)
        await self.db.flush()
        await self.db.refresh(bottle_group)
        return bottle_group
    
    async def batch_create_bottle_groups(self, bottle_groups: List[SamplingBottleGroup]) -> List[SamplingBottleGroup]:
        """批量创建瓶组"""
        self.db.add_all(bottle_groups)
        await self.db.flush()
        for bottle_group in bottle_groups:
            await self.db.refresh(bottle_group)
        return bottle_groups
    
    async def get_bottle_group_by_id(self, bottle_group_id: int) -> Optional[SamplingBottleGroup]:
        """根据ID获取瓶组"""
        stmt = select(SamplingBottleGroup).where(SamplingBottleGroup.id == bottle_group_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_bottle_groups_by_task_id(self, task_id: int) -> List[SamplingBottleGroup]:
        """根据任务ID获取瓶组列表"""
        stmt = select(SamplingBottleGroup).where(
            SamplingBottleGroup.sampling_task_id == task_id
        ).order_by(
            SamplingBottleGroup.cycle_number,
            SamplingBottleGroup.bottle_group_sequence
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_bottle_groups_by_assignment_id(self, assignment_id: int) -> List[SamplingBottleGroup]:
        """根据任务执行指派ID获取瓶组列表"""
        stmt = select(SamplingBottleGroup).where(
            SamplingBottleGroup.task_assignment_id == assignment_id
        ).order_by(SamplingBottleGroup.bottle_group_sequence)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_bottle_groups_with_detection_params(self, task_id: int) -> List[SamplingBottleGroup]:
        """获取包含检测参数的瓶组列表"""
        stmt = select(SamplingBottleGroup).options(
            selectinload(SamplingBottleGroup.detection_params)
        ).where(
            SamplingBottleGroup.sampling_task_id == task_id
        ).order_by(
            SamplingBottleGroup.cycle_number,
            SamplingBottleGroup.bottle_group_sequence
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def update_bottle_group(self, bottle_group_id: int, update_data: dict) -> Optional[SamplingBottleGroup]:
        """更新瓶组"""
        stmt = update(SamplingBottleGroup).where(SamplingBottleGroup.id == bottle_group_id).values(**update_data)
        await self.db.execute(stmt)
        return await self.get_bottle_group_by_id(bottle_group_id)
    
    async def delete_bottle_group(self, bottle_group_id: int) -> bool:
        """删除瓶组"""
        stmt = delete(SamplingBottleGroup).where(SamplingBottleGroup.id == bottle_group_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_bottle_groups_by_task_id(self, task_id: int) -> int:
        """根据任务ID删除瓶组"""
        stmt = delete(SamplingBottleGroup).where(SamplingBottleGroup.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        return result.rowcount
    
    async def delete_bottle_groups_by_assignment_id(self, assignment_id: int) -> int:
        """根据任务执行指派ID删除瓶组"""
        stmt = delete(SamplingBottleGroup).where(SamplingBottleGroup.task_assignment_id == assignment_id)
        result = await self.db.execute(stmt)
        return result.rowcount
    
    async def get_max_bottle_group_sequence_by_task(self, task_id: int) -> int:
        """获取任务下的最大瓶组序号"""
        stmt = select(SamplingBottleGroup.bottle_group_sequence).where(
            SamplingBottleGroup.sampling_task_id == task_id
        ).order_by(SamplingBottleGroup.bottle_group_sequence.desc()).limit(1)
        result = await self.db.execute(stmt)
        max_sequence = result.scalar_one_or_none()
        return max_sequence or 0
    
    async def create_detection_param(self, detection_param: SamplingBottleGroupDetectionParam) -> SamplingBottleGroupDetectionParam:
        """创建检测参数关联"""
        self.db.add(detection_param)
        await self.db.flush()
        await self.db.refresh(detection_param)
        return detection_param
    
    async def batch_create_detection_params(self, detection_params: List[SamplingBottleGroupDetectionParam]) -> List[SamplingBottleGroupDetectionParam]:
        """批量创建检测参数关联"""
        self.db.add_all(detection_params)
        await self.db.flush()
        for param in detection_params:
            await self.db.refresh(param)
        return detection_params
    
    async def get_detection_params_by_bottle_group_id(self, bottle_group_id: int) -> List[SamplingBottleGroupDetectionParam]:
        """根据瓶组ID获取检测参数列表"""
        stmt = select(SamplingBottleGroupDetectionParam).where(
            SamplingBottleGroupDetectionParam.bottle_group_id == bottle_group_id
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def delete_detection_params_by_bottle_group_id(self, bottle_group_id: int) -> int:
        """根据瓶组ID删除检测参数关联"""
        stmt = delete(SamplingBottleGroupDetectionParam).where(
            SamplingBottleGroupDetectionParam.bottle_group_id == bottle_group_id
        )
        result = await self.db.execute(stmt)
        return result.rowcount
