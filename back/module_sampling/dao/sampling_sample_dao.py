"""
采样样品DAO
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, update, and_
from sqlalchemy.orm import selectinload

from module_sampling.entity.do.sampling_sample_do import SamplingSample


class SamplingSampleDAO:
    """采样样品DAO"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_sample(self, sample: SamplingSample) -> SamplingSample:
        """创建样品"""
        self.db.add(sample)
        await self.db.flush()
        await self.db.refresh(sample)
        return sample
    
    async def batch_create_samples(self, samples: List[SamplingSample]) -> List[SamplingSample]:
        """批量创建样品"""
        self.db.add_all(samples)
        await self.db.flush()
        for sample in samples:
            await self.db.refresh(sample)
        return samples
    
    async def get_sample_by_id(self, sample_id: int) -> Optional[SamplingSample]:
        """根据ID获取样品"""
        stmt = select(SamplingSample).where(SamplingSample.id == sample_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_samples_by_task_id(self, task_id: int) -> List[SamplingSample]:
        """根据任务ID获取样品列表"""
        stmt = select(SamplingSample).where(
            SamplingSample.sampling_task_id == task_id
        ).order_by(
            SamplingSample.cycle_number,
            SamplingSample.sample_number
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_samples_by_assignment_id(self, assignment_id: int) -> List[SamplingSample]:
        """根据任务执行指派ID获取样品列表"""
        stmt = select(SamplingSample).where(
            SamplingSample.task_assignment_id == assignment_id
        ).order_by(SamplingSample.sample_number)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_samples_by_cycle_info(
        self, 
        task_id: int, 
        cycle_number: int, 
        cycle_type: Optional[str] = None,
        detection_category: Optional[str] = None,
        point_name: Optional[str] = None
    ) -> List[SamplingSample]:
        """根据周期信息获取样品列表"""
        conditions = [
            SamplingSample.sampling_task_id == task_id,
            SamplingSample.cycle_number == cycle_number
        ]
        
        if cycle_type:
            conditions.append(SamplingSample.cycle_type == cycle_type)
        if detection_category:
            conditions.append(SamplingSample.detection_category == detection_category)
        if point_name:
            conditions.append(SamplingSample.point_name == point_name)
        
        stmt = select(SamplingSample).where(and_(*conditions)).order_by(SamplingSample.sample_number)
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def update_sample(self, sample_id: int, update_data: dict) -> Optional[SamplingSample]:
        """更新样品"""
        stmt = update(SamplingSample).where(SamplingSample.id == sample_id).values(**update_data)
        await self.db.execute(stmt)
        return await self.get_sample_by_id(sample_id)
    
    async def delete_sample(self, sample_id: int) -> bool:
        """删除样品"""
        stmt = delete(SamplingSample).where(SamplingSample.id == sample_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def delete_samples_by_task_id(self, task_id: int) -> int:
        """根据任务ID删除样品"""
        stmt = delete(SamplingSample).where(SamplingSample.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        return result.rowcount
    
    async def delete_samples_by_assignment_id(self, assignment_id: int) -> int:
        """根据任务执行指派ID删除样品"""
        stmt = delete(SamplingSample).where(SamplingSample.task_assignment_id == assignment_id)
        result = await self.db.execute(stmt)
        return result.rowcount
    
    async def get_max_sample_number_by_cycle(
        self, 
        task_id: int, 
        cycle_number: int, 
        cycle_type: Optional[str] = None,
        detection_category: Optional[str] = None,
        point_name: Optional[str] = None
    ) -> int:
        """获取指定周期条件下的最大样品序号"""
        conditions = [
            SamplingSample.sampling_task_id == task_id,
            SamplingSample.cycle_number == cycle_number
        ]
        
        if cycle_type:
            conditions.append(SamplingSample.cycle_type == cycle_type)
        if detection_category:
            conditions.append(SamplingSample.detection_category == detection_category)
        if point_name:
            conditions.append(SamplingSample.point_name == point_name)
        
        stmt = select(SamplingSample.sample_number).where(and_(*conditions)).order_by(SamplingSample.sample_number.desc()).limit(1)
        result = await self.db.execute(stmt)
        max_number = result.scalar_one_or_none()
        return max_number or 0
