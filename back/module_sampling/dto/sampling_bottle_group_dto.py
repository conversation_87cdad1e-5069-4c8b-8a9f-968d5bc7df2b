"""
采样瓶组DTO
"""

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel
from typing import List, Optional
from datetime import datetime


class SamplingBottleGroupCreateDTO(BaseModel):
    """创建采样瓶组DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_id: int
    task_assignment_id: int
    bottle_maintenance_id: Optional[int] = None
    bottle_group_code: str
    bottle_group_sequence: int
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    bottle_group_description: Optional[str] = None
    remark: Optional[str] = None


class SamplingBottleGroupUpdateDTO(BaseModel):
    """更新采样瓶组DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    status: Optional[int] = None
    bottle_group_description: Optional[str] = None
    remark: Optional[str] = None


class SamplingBottleGroupDTO(BaseModel):
    """采样瓶组DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    id: int
    sampling_task_id: int
    task_assignment_id: int
    bottle_maintenance_id: Optional[int] = None
    bottle_group_code: str
    bottle_group_sequence: int
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    status: int
    bottle_group_description: Optional[str] = None
    remark: Optional[str] = None
    create_by: Optional[int] = None
    create_time: Optional[datetime] = None
    update_by: Optional[int] = None
    update_time: Optional[datetime] = None


class SamplingBottleGroupDetectionParamCreateDTO(BaseModel):
    """创建采样瓶组检测参数关联DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    bottle_group_id: int
    project_quotation_item_id: int
    technical_manual_id: Optional[int] = None
    qualification_code: Optional[str] = None
    classification: Optional[str] = None
    category: str
    parameter: str
    method: str
    limitation_scope: Optional[str] = None
    sample_source: Optional[str] = None
    point_name: Optional[str] = None
    point_count: int = 1
    cycle_type: Optional[str] = None
    cycle_count: int = 1
    frequency: int = 1
    sample_count: int = 1


class SamplingBottleGroupQueryDTO(BaseModel):
    """采样瓶组查询DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_id: Optional[int] = None
    task_assignment_id: Optional[int] = None
    bottle_maintenance_id: Optional[int] = None
    cycle_number: Optional[int] = None
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    status: Optional[int] = None
    page_num: int = 1
    page_size: int = 10
