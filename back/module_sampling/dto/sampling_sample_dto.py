"""
采样样品DTO
"""

from pydantic import BaseModel, ConfigDict
from pydantic.alias_generators import to_camel
from typing import List, Optional
from datetime import datetime


class SamplingSampleCreateDTO(BaseModel):
    """创建采样样品DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_id: int
    task_assignment_id: int
    sample_number: int
    sample_code: Optional[str] = None
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    sample_description: Optional[str] = None
    remark: Optional[str] = None


class SamplingSampleUpdateDTO(BaseModel):
    """更新采样样品DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sample_code: Optional[str] = None
    status: Optional[int] = None
    collection_date: Optional[datetime] = None
    collection_time: Optional[datetime] = None
    collector_id: Optional[int] = None
    collection_location: Optional[str] = None
    sample_description: Optional[str] = None
    remark: Optional[str] = None


class SamplingSampleDTO(BaseModel):
    """采样样品DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    id: int
    sampling_task_id: int
    task_assignment_id: int
    sample_number: int
    sample_code: Optional[str] = None
    cycle_number: int
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    status: int
    collection_date: Optional[datetime] = None
    collection_time: Optional[datetime] = None
    collector_id: Optional[int] = None
    collection_location: Optional[str] = None
    sample_description: Optional[str] = None
    remark: Optional[str] = None
    create_by: Optional[int] = None
    create_time: Optional[datetime] = None
    update_by: Optional[int] = None
    update_time: Optional[datetime] = None


class SamplingSampleQueryDTO(BaseModel):
    """采样样品查询DTO"""
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True, populate_by_name=True)
    
    sampling_task_id: Optional[int] = None
    task_assignment_id: Optional[int] = None
    cycle_number: Optional[int] = None
    cycle_type: Optional[str] = None
    detection_category: Optional[str] = None
    point_name: Optional[str] = None
    status: Optional[int] = None
    collector_id: Optional[int] = None
    page_num: int = 1
    page_size: int = 10
