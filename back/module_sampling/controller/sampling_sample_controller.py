"""
采样样品控制器
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from config.database import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_sampling.service.sampling_sample_service import SamplingSampleService
from module_sampling.dto.sampling_sample_dto import (
    SamplingSampleCreateDTO,
    SamplingSampleUpdateDTO,
    SamplingSampleDTO,
    SamplingSampleQueryDTO
)
from utils.response_util import ResponseUtil
from utils.log_util import logger

router = APIRouter(prefix="/sampling/samples", tags=["采样样品管理"])


@router.post("/", summary="创建样品")
async def create_sample(
    create_dto: SamplingSampleCreateDTO,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    创建样品
    
    Args:
        create_dto: 创建样品DTO
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        创建的样品信息
    """
    try:
        service = SamplingSampleService(db)
        sample = await service.create_sample(create_dto, current_user)
        
        return ResponseUtil.success(data=sample)
        
    except Exception as e:
        logger.error(f"创建样品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{sample_id}", summary="更新样品")
async def update_sample(
    sample_id: int,
    update_dto: SamplingSampleUpdateDTO,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    更新样品
    
    Args:
        sample_id: 样品ID
        update_dto: 更新样品DTO
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新后的样品信息
    """
    try:
        service = SamplingSampleService(db)
        sample = await service.update_sample(sample_id, update_dto, current_user)
        
        return ResponseUtil.success(data=sample)
        
    except Exception as e:
        logger.error(f"更新样品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{sample_id}", summary="获取样品详情")
async def get_sample_detail(
    sample_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取样品详情
    
    Args:
        sample_id: 样品ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        样品详情
    """
    try:
        service = SamplingSampleService(db)
        sample = await service.get_sample_by_id(sample_id)
        
        return ResponseUtil.success(data=sample)
        
    except Exception as e:
        logger.error(f"获取样品详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}", summary="根据任务ID获取样品列表")
async def get_samples_by_task_id(
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    根据任务ID获取样品列表
    
    Args:
        task_id: 任务ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        样品列表
    """
    try:
        service = SamplingSampleService(db)
        samples = await service.get_samples_by_task_id(task_id)
        
        return ResponseUtil.success(data=samples)
        
    except Exception as e:
        logger.error(f"获取样品列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/assignment/{assignment_id}", summary="根据任务执行指派ID获取样品列表")
async def get_samples_by_assignment_id(
    assignment_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    根据任务执行指派ID获取样品列表
    
    Args:
        assignment_id: 任务执行指派ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        样品列表
    """
    try:
        service = SamplingSampleService(db)
        samples = await service.get_samples_by_assignment_id(assignment_id)
        
        return ResponseUtil.success(data=samples)
        
    except Exception as e:
        logger.error(f"获取样品列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{sample_id}", summary="删除样品")
async def delete_sample(
    sample_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    删除样品
    
    Args:
        sample_id: 样品ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        删除结果
    """
    try:
        service = SamplingSampleService(db)
        result = await service.delete_sample(sample_id, current_user)
        
        return ResponseUtil.success(data={"deleted": result})
        
    except Exception as e:
        logger.error(f"删除样品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/batch/status", summary="批量更新样品状态")
async def batch_update_sample_status(
    sample_ids: List[int],
    status: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    批量更新样品状态
    
    Args:
        sample_ids: 样品ID列表
        status: 新状态
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        service = SamplingSampleService(db)
        updated_count = await service.batch_update_sample_status(sample_ids, status, current_user)
        
        return ResponseUtil.success(data={"updated_count": updated_count})
        
    except Exception as e:
        logger.error(f"批量更新样品状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/options", summary="获取样品状态选项")
async def get_sample_status_options(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取样品状态选项
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        状态选项列表
    """
    try:
        service = SamplingSampleService(db)
        options = service.get_sample_status_options()
        
        return ResponseUtil.success(data=options)
        
    except Exception as e:
        logger.error(f"获取样品状态选项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
