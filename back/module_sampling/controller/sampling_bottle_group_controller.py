"""
采样瓶组控制器
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from config.database import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
from module_sampling.dto.sampling_bottle_group_dto import (
    SamplingBottleGroupCreateDTO,
    SamplingBottleGroupUpdateDTO,
    SamplingBottleGroupDTO,
    SamplingBottleGroupQueryDTO
)
from utils.response_util import ResponseUtil
from utils.log_util import logger

router = APIRouter(prefix="/sampling/bottle-groups", tags=["采样瓶组管理"])


@router.post("/", summary="创建瓶组")
async def create_bottle_group(
    create_dto: SamplingBottleGroupCreateDTO,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    创建瓶组
    
    Args:
        create_dto: 创建瓶组DTO
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        创建的瓶组信息
    """
    try:
        service = SamplingBottleGroupService(db)
        bottle_group = await service.create_bottle_group(create_dto, current_user)
        
        return ResponseUtil.success(data=bottle_group)
        
    except Exception as e:
        logger.error(f"创建瓶组失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{bottle_group_id}", summary="更新瓶组")
async def update_bottle_group(
    bottle_group_id: int,
    update_dto: SamplingBottleGroupUpdateDTO,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    更新瓶组
    
    Args:
        bottle_group_id: 瓶组ID
        update_dto: 更新瓶组DTO
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新后的瓶组信息
    """
    try:
        service = SamplingBottleGroupService(db)
        bottle_group = await service.update_bottle_group(bottle_group_id, update_dto, current_user)
        
        return ResponseUtil.success(data=bottle_group)
        
    except Exception as e:
        logger.error(f"更新瓶组失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{bottle_group_id}", summary="获取瓶组详情")
async def get_bottle_group_detail(
    bottle_group_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取瓶组详情
    
    Args:
        bottle_group_id: 瓶组ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        瓶组详情
    """
    try:
        service = SamplingBottleGroupService(db)
        bottle_group = await service.get_bottle_group_by_id(bottle_group_id)
        
        return ResponseUtil.success(data=bottle_group)
        
    except Exception as e:
        logger.error(f"获取瓶组详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}", summary="根据任务ID获取瓶组列表")
async def get_bottle_groups_by_task_id(
    task_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    根据任务ID获取瓶组列表
    
    Args:
        task_id: 任务ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        瓶组列表
    """
    try:
        service = SamplingBottleGroupService(db)
        bottle_groups = await service.get_bottle_groups_by_task_id(task_id)
        
        return ResponseUtil.success(data=bottle_groups)
        
    except Exception as e:
        logger.error(f"获取瓶组列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/assignment/{assignment_id}", summary="根据任务执行指派ID获取瓶组列表")
async def get_bottle_groups_by_assignment_id(
    assignment_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    根据任务执行指派ID获取瓶组列表
    
    Args:
        assignment_id: 任务执行指派ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        瓶组列表
    """
    try:
        service = SamplingBottleGroupService(db)
        bottle_groups = await service.get_bottle_groups_by_assignment_id(assignment_id)
        
        return ResponseUtil.success(data=bottle_groups)
        
    except Exception as e:
        logger.error(f"获取瓶组列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{bottle_group_id}", summary="删除瓶组")
async def delete_bottle_group(
    bottle_group_id: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    删除瓶组
    
    Args:
        bottle_group_id: 瓶组ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        删除结果
    """
    try:
        service = SamplingBottleGroupService(db)
        result = await service.delete_bottle_group(bottle_group_id, current_user)
        
        return ResponseUtil.success(data={"deleted": result})
        
    except Exception as e:
        logger.error(f"删除瓶组失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/batch/status", summary="批量更新瓶组状态")
async def batch_update_bottle_group_status(
    bottle_group_ids: List[int],
    status: int,
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    批量更新瓶组状态
    
    Args:
        bottle_group_ids: 瓶组ID列表
        status: 新状态
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        service = SamplingBottleGroupService(db)
        updated_count = await service.batch_update_bottle_group_status(bottle_group_ids, status, current_user)
        
        return ResponseUtil.success(data={"updated_count": updated_count})
        
    except Exception as e:
        logger.error(f"批量更新瓶组状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/options", summary="获取瓶组状态选项")
async def get_bottle_group_status_options(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取瓶组状态选项
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        状态选项列表
    """
    try:
        service = SamplingBottleGroupService(db)
        options = service.get_bottle_group_status_options()
        
        return ResponseUtil.success(data=options)
        
    except Exception as e:
        logger.error(f"获取瓶组状态选项失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
