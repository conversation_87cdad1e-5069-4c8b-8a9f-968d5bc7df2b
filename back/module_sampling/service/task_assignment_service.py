"""
采样任务执行人指派服务
"""

import json
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from module_sampling.dao.sampling_task_assignment_dao import SamplingTaskAssignmentDAO
from module_sampling.entity.do.sampling_task_assignment_do import SamplingTaskAssignment
from module_sampling.dto.sampling_task_assignment_dto import (
    SamplingTaskAssignmentCreateDTO,
    SamplingTaskAssignmentUpdateDTO,
    SamplingTaskAssignmentDTO,
    TaskAssignmentRequestDTO
)
from module_admin.entity.do.user_do import SysUser
from exceptions.exception import ServiceException
from module_sampling.entity.do.sampling_sample_do import SamplingSample
from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_sampling.entity.do.sampling_bottle_group_detection_param_do import SamplingBottleGroupDetectionParam
from module_sampling.dao.sampling_sample_dao import SamplingSampleDAO
from module_sampling.dao.sampling_bottle_group_dao import SamplingBottleGroupDAO
from config.constant import AnalysisTypeConstant
from utils.log_util import logger


class TaskAssignmentService:
    """采样任务执行人指派服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.assignment_dao = SamplingTaskAssignmentDAO(db)
        self.sample_dao = SamplingSampleDAO(db)
        self.bottle_group_dao = SamplingBottleGroupDAO(db)
    
    async def create_or_update_assignments(
        self,
        request: TaskAssignmentRequestDTO,
        current_user_id: int
    ) -> List[SamplingTaskAssignmentDTO]:
        """创建或更新执行人指派"""
        try:
            result_assignments = []
            
            for assignment_data in request.assignments:
                # 检查是否已存在相同分组的指派
                existing_assignment = await self.assignment_dao.get_assignment_by_group_key(
                    task_id=assignment_data.sampling_task_id,
                    cycle_number=assignment_data.cycle_number,
                    cycle_type=assignment_data.cycle_type,
                    detection_category=assignment_data.detection_category,
                    point_name=assignment_data.point_name
                )
                
                if existing_assignment:
                    # 更新现有指派
                    existing_assignment.assigned_user_ids = json.dumps(assignment_data.assigned_user_ids)
                    existing_assignment.update_by = current_user_id
                    updated_assignment = await self.assignment_dao.update_assignment(existing_assignment)
                    result_assignments.append(await self._convert_to_dto(updated_assignment))
                else:
                    # 创建新指派
                    new_assignment = SamplingTaskAssignment(
                        sampling_task_id=assignment_data.sampling_task_id,
                        cycle_number=assignment_data.cycle_number,
                        cycle_type=assignment_data.cycle_type,
                        detection_category=assignment_data.detection_category,
                        point_name=assignment_data.point_name,
                        assigned_user_ids=json.dumps(assignment_data.assigned_user_ids),
                        create_by=current_user_id,
                        update_by=current_user_id
                    )
                    created_assignment = await self.assignment_dao.create_assignment(new_assignment)
                    result_assignments.append(await self._convert_to_dto(created_assignment))
            
            await self.db.commit()
            return result_assignments
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"创建或更新任务指派失败: {str(e)}")
    
    async def get_assignments_by_task_id(self, task_id: int) -> List[SamplingTaskAssignmentDTO]:
        """根据任务ID获取所有指派"""
        assignments = await self.assignment_dao.get_assignments_by_task_id(task_id)
        return [await self._convert_to_dto(assignment) for assignment in assignments]
    
    async def get_assignments_by_user_id(self, user_id: int) -> List[SamplingTaskAssignmentDTO]:
        """根据用户ID获取分配给该用户的任务指派"""
        assignments = await self.assignment_dao.get_assignments_by_user_id(user_id)
        return [await self._convert_to_dto(assignment) for assignment in assignments]
    
    async def delete_assignment(self, assignment_id: int) -> bool:
        """删除任务指派"""
        try:
            result = await self.assignment_dao.delete_assignment(assignment_id)
            await self.db.commit()
            return result
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"删除任务指派失败: {str(e)}")
    
    async def _convert_to_dto(self, assignment: SamplingTaskAssignment) -> SamplingTaskAssignmentDTO:
        """将实体转换为DTO"""
        # 解析分配的用户ID
        assigned_user_ids = []
        if assignment.assigned_user_ids:
            try:
                assigned_user_ids = json.loads(assignment.assigned_user_ids)
            except:
                assigned_user_ids = []

        # 获取用户名称
        assigned_user_names = []
        if assigned_user_ids:
            stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
            result = await self.db.execute(stmt)
            assigned_user_names = [row[0] for row in result.fetchall()]

        return SamplingTaskAssignmentDTO(
            id=assignment.id,
            sampling_task_id=assignment.sampling_task_id,
            cycle_number=assignment.cycle_number,
            cycle_type=assignment.cycle_type,
            detection_category=assignment.detection_category,
            point_name=assignment.point_name,
            assigned_user_ids=assigned_user_ids,
            assigned_user_names=assigned_user_names,
            create_by=assignment.create_by,
            create_time=assignment.create_time,
            update_by=assignment.update_by,
            update_time=assignment.update_time
        )

    async def get_execution_list_by_user_id(self, user_id: int) -> List[dict]:
        """根据用户ID获取执行任务列表（包含任务详细信息）"""
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_customer.entity.do.customer_do import Customer

        assignments = await self.assignment_dao.get_assignments_by_user_id(user_id)
        execution_list = []

        for assignment in assignments:
            # 获取任务详细信息
            stmt = select(SamplingTask).options(
                selectinload(SamplingTask.project_quotation)
            ).where(SamplingTask.id == assignment.sampling_task_id)
            result = await self.db.execute(stmt)
            task = result.scalar_one_or_none()

            if task:
                # 解析分配的用户ID和名称
                assigned_user_ids = []
                assigned_user_names = []
                if assignment.assigned_user_ids:
                    try:
                        assigned_user_ids = json.loads(assignment.assigned_user_ids)
                        if assigned_user_ids:
                            user_stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
                            user_result = await self.db.execute(user_stmt)
                            assigned_user_names = [row[0] for row in user_result.fetchall()]
                    except:
                        pass

                execution_item = {
                    'id': assignment.id,
                    'sampling_task_id': assignment.sampling_task_id,
                    'task_code': task.task_code,
                    'task_name': task.task_name,
                    'status': task.status,
                    'planned_start_date': task.planned_start_date,
                    'planned_end_date': task.planned_end_date,
                    'actual_start_date': task.actual_start_date,
                    'actual_end_date': task.actual_end_date,
                    'cycle_number': assignment.cycle_number,
                    'cycle_type': assignment.cycle_type,
                    'detection_category': assignment.detection_category,
                    'point_name': assignment.point_name,
                    'assigned_user_ids': assigned_user_ids,
                    'assigned_user_names': assigned_user_names,
                    'project_name': task.project_quotation.project_name if task.project_quotation else '',
                    'customer_name': task.project_quotation.customer_name if task.project_quotation else '',
                    'create_time': assignment.create_time,
                    'update_time': assignment.update_time
                }
                execution_list.append(execution_item)

        return execution_list

    async def get_execution_task_detail(self, assignment_id: int) -> dict:
        """获取执行任务详情，包括过滤后的周期条目"""
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
        from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
        from module_sampling.dto.detection_cycle_item_dto import DetectionCycleItemDTO
        
        # 获取执行任务指派信息
        assignment = await self.assignment_dao.get_assignment_by_id(assignment_id)
        if not assignment:
            raise ServiceException(message="执行任务不存在")
        
        # 获取采样任务信息
        stmt = select(SamplingTask).options(
            selectinload(SamplingTask.project_quotation),
            selectinload(SamplingTask.responsible_user)
        ).where(SamplingTask.id == assignment.sampling_task_id)
        result = await self.db.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            raise ServiceException(message="采样任务不存在")
        
        # 获取该执行任务相关的周期条目（根据cycle_number、cycle_type、detection_category、point_name过滤）
        cycle_items_query = (
            select(DetectionCycleItem)
            .join(SamplingTaskCycleItem, DetectionCycleItem.id == SamplingTaskCycleItem.detection_cycle_item_id)
            .join(ProjectQuotationItem, DetectionCycleItem.project_quotation_item_id == ProjectQuotationItem.id)
            .where(
                SamplingTaskCycleItem.sampling_task_id == assignment.sampling_task_id,
                DetectionCycleItem.cycle_number == assignment.cycle_number
            )
        )
        
        # 根据执行任务的分组条件进一步过滤
        if assignment.cycle_type:
            cycle_items_query = cycle_items_query.where(ProjectQuotationItem.cycle_type == assignment.cycle_type)
        if assignment.detection_category:
            cycle_items_query = cycle_items_query.where(ProjectQuotationItem.category == assignment.detection_category)
        if assignment.point_name:
            cycle_items_query = cycle_items_query.where(ProjectQuotationItem.point_name == assignment.point_name)
        
        cycle_items_result = await self.db.execute(cycle_items_query)
        cycle_items = cycle_items_result.scalars().all()
        
        # 转换周期条目为DTO并过滤现场直读类型
        cycle_items_dto = []
        filtered_cycle_items = []  # 用于样品和瓶组生成的过滤后条目

        for cycle_item in cycle_items:
            # 获取项目报价明细信息
            item_stmt = select(ProjectQuotationItem).where(ProjectQuotationItem.id == cycle_item.project_quotation_item_id)
            item_result = await self.db.execute(item_stmt)
            quotation_item = item_result.scalar_one_or_none()

            cycle_item_dto = DetectionCycleItemDTO(
                id=cycle_item.id,
                project_quotation_id=cycle_item.project_quotation_id,
                project_quotation_item_id=cycle_item.project_quotation_item_id,
                cycle_number=cycle_item.cycle_number,
                status=cycle_item.status,
                status_label=cycle_item.status_label
            )

            # 检查是否为现场直读类型
            is_on_site_check = False
            technical_manual = None

            if quotation_item:
                cycle_item_dto.detection_qualification = quotation_item.qualification_code
                cycle_item_dto.detection_classification = quotation_item.classification
                cycle_item_dto.detection_category = quotation_item.category
                cycle_item_dto.detection_parameter = quotation_item.parameter
                cycle_item_dto.detection_method = quotation_item.method
                cycle_item_dto.sample_source = quotation_item.sample_source
                cycle_item_dto.point_name = quotation_item.point_name
                cycle_item_dto.cycle_type = quotation_item.cycle_type

                # 查询技术手册判断分析类型
                if quotation_item.qualification_code:
                    from module_basedata.entity.do.technical_manual_do import TechnicalManual
                    manual_stmt = select(TechnicalManual).where(
                        TechnicalManual.qualification_code == quotation_item.qualification_code
                    )
                    manual_result = await self.db.execute(manual_stmt)
                    technical_manual = manual_result.scalar_one_or_none()

                    if technical_manual and technical_manual.analysis_type == AnalysisTypeConstant.ON_SITE_CHECK:
                        is_on_site_check = True

            cycle_items_dto.append(cycle_item_dto)

            # 如果不是现场直读类型，添加到过滤后的列表中
            if not is_on_site_check and quotation_item:
                filtered_item = {
                    'id': cycle_item.id,
                    'project_quotation_item_id': cycle_item.project_quotation_item_id,
                    'qualification_code': quotation_item.qualification_code,
                    'classification': quotation_item.classification,
                    'category': quotation_item.category,
                    'parameter': quotation_item.parameter,
                    'method': quotation_item.method,
                    'limitation_scope': quotation_item.limitation_scope,
                    'sample_source': quotation_item.sample_source,
                    'point_name': quotation_item.point_name,
                    'point_count': quotation_item.point_count,
                    'cycle_type': quotation_item.cycle_type,
                    'cycle_count': quotation_item.cycle_count,
                    'frequency': quotation_item.frequency,
                    'sample_count': quotation_item.sample_count,
                    'technical_manual': technical_manual.to_dict() if technical_manual else {}
                }
                filtered_cycle_items.append(filtered_item)
        
        # 解析分配的用户ID和名称
        assigned_user_ids = []
        assigned_user_names = []
        if assignment.assigned_user_ids:
            try:
                assigned_user_ids = json.loads(assignment.assigned_user_ids)
                if assigned_user_ids:
                    user_stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
                    user_result = await self.db.execute(user_stmt)
                    assigned_user_names = [row[0] for row in user_result.fetchall()]
            except:
                pass
        
        # 构建返回结果
        return {
            'id': assignment.id,
            'sampling_task_id': assignment.sampling_task_id,
            'task_code': task.task_code,
            'task_name': task.task_name,
            'description': task.description,
            'status': task.status,
            'planned_start_date': task.planned_start_date,
            'planned_end_date': task.planned_end_date,
            'actual_start_date': task.actual_start_date,
            'actual_end_date': task.actual_end_date,
            'cycle_number': assignment.cycle_number,
            'cycle_type': assignment.cycle_type,
            'detection_category': assignment.detection_category,
            'point_name': assignment.point_name,
            'assigned_user_ids': assigned_user_ids,
            'assigned_user_names': assigned_user_names,
            'project_name': task.project_quotation.project_name if task.project_quotation else '',
            'customer_name': task.project_quotation.customer_name if task.project_quotation else '',
            'responsible_user_name': task.responsible_user.nick_name if task.responsible_user else '',
            'cycle_items': [item.dict() for item in cycle_items_dto],
            'filtered_cycle_items': filtered_cycle_items,  # 用于样品和瓶组生成的过滤后条目
            'create_time': assignment.create_time,
            'update_time': assignment.update_time
        }

    async def get_all_execution_list(self) -> List[dict]:
        """获取所有执行任务列表（仅超级管理员）"""
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_customer.entity.do.customer_do import Customer

        # 查询所有执行人指派记录
        stmt = (
            select(SamplingTaskAssignment, SamplingTask, ProjectQuotation, Customer)
            .join(SamplingTask, SamplingTaskAssignment.sampling_task_id == SamplingTask.id)
            .join(ProjectQuotation, SamplingTask.project_quotation_id == ProjectQuotation.id, isouter=True)
            .join(Customer, ProjectQuotation.customer_id == Customer.customer_id, isouter=True)
            .order_by(SamplingTaskAssignment.create_time.desc())
        )

        result = await self.db.execute(stmt)
        records = result.fetchall()

        execution_list = []
        for assignment, task, project_quotation, customer in records:
            # 解析执行人ID和名称
            assigned_user_ids = []
            assigned_user_names = []
            if assignment.assigned_user_ids:
                try:
                    assigned_user_ids = json.loads(assignment.assigned_user_ids)
                    if assigned_user_ids:
                        user_stmt = select(SysUser.nick_name).where(SysUser.user_id.in_(assigned_user_ids))
                        user_result = await self.db.execute(user_stmt)
                        assigned_user_names = [row[0] for row in user_result.fetchall()]
                except:
                    pass

            execution_item = {
                'id': assignment.id,
                'sampling_task_id': assignment.sampling_task_id,
                'task_code': task.task_code,
                'task_name': task.task_name,
                'status': task.status,
                'planned_start_date': task.planned_start_date,
                'planned_end_date': task.planned_end_date,
                'actual_start_date': task.actual_start_date,
                'actual_end_date': task.actual_end_date,
                'cycle_number': assignment.cycle_number,
                'cycle_type': assignment.cycle_type,
                'detection_category': assignment.detection_category,
                'point_name': assignment.point_name,
                'assigned_user_ids': assigned_user_ids,
                'assigned_user_names': assigned_user_names,
                'project_name': project_quotation.project_name if project_quotation else '',
                'customer_name': customer.customer_name if customer else '',
                'create_time': assignment.create_time,
                'update_time': assignment.update_time
            }
            execution_list.append(execution_item)

        return execution_list

    async def generate_samples_and_bottle_groups(self, assignment_id: int, current_user_id: int) -> dict:
        """
        为采样任务执行指派生成样品和瓶组

        Args:
            assignment_id: 任务执行指派ID
            current_user_id: 当前用户ID

        Returns:
            生成结果统计
        """
        try:
            # 获取任务执行指派信息
            assignment = await self.assignment_dao.get_assignment_by_id(assignment_id)
            if not assignment:
                raise ServiceException("执行任务不存在")

            # 获取任务详情和相关的周期条目
            task_detail = await self.get_execution_task_detail(assignment_id)

            # 生成样品记录
            samples_created = await self._generate_samples_for_assignment(
                assignment, task_detail, current_user_id
            )

            # 生成瓶组记录
            bottle_groups_created = await self._generate_bottle_groups_for_assignment(
                assignment, task_detail, current_user_id
            )

            await self.db.commit()

            return {
                'assignment_id': assignment_id,
                'samples_created': len(samples_created),
                'bottle_groups_created': len(bottle_groups_created),
                'samples': [sample.to_dict() for sample in samples_created],
                'bottle_groups': [bg.to_dict() for bg in bottle_groups_created]
            }

        except Exception as e:
            await self.db.rollback()
            logger.error(f"生成样品和瓶组失败: {str(e)}")
            raise ServiceException(f"生成样品和瓶组失败: {str(e)}")

    async def _generate_samples_for_assignment(
        self,
        assignment: SamplingTaskAssignment,
        task_detail: dict,
        current_user_id: int
    ) -> List[SamplingSample]:
        """
        为任务执行指派生成样品记录

        Args:
            assignment: 任务执行指派
            task_detail: 任务详情（包含周期条目）
            current_user_id: 当前用户ID

        Returns:
            创建的样品列表
        """
        samples_to_create = []

        # 获取过滤后的周期条目（排除现场直读类型）
        filtered_cycle_items = task_detail.get('filtered_cycle_items', [])

        # 按报价明细分组计算样品数
        quotation_items_map = {}
        for cycle_item in filtered_cycle_items:
            quotation_item_id = cycle_item.get('project_quotation_item_id')
            if quotation_item_id not in quotation_items_map:
                quotation_items_map[quotation_item_id] = cycle_item

        # 计算总样品数（取最大值）
        max_sample_count = 0
        for quotation_item in quotation_items_map.values():
            frequency = quotation_item.get('frequency', 1)
            sample_count = quotation_item.get('sample_count', 1)
            total_count = frequency * sample_count
            max_sample_count = max(max_sample_count, total_count)

        # 获取当前周期条件下已有的最大样品序号
        current_max_number = await self.sample_dao.get_max_sample_number_by_cycle(
            assignment.sampling_task_id,
            assignment.cycle_number,
            assignment.cycle_type,
            assignment.detection_category,
            assignment.point_name
        )

        # 生成样品记录
        for i in range(max_sample_count):
            sample_number = current_max_number + i + 1

            sample = SamplingSample(
                sampling_task_id=assignment.sampling_task_id,
                task_assignment_id=assignment.id,
                sample_number=sample_number,
                cycle_number=assignment.cycle_number,
                cycle_type=assignment.cycle_type,
                detection_category=assignment.detection_category,
                point_name=assignment.point_name,
                status=0,  # 待采集
                create_by=current_user_id
            )
            samples_to_create.append(sample)

        # 批量创建样品
        if samples_to_create:
            created_samples = await self.sample_dao.batch_create_samples(samples_to_create)
            return created_samples

        return []

    async def _generate_bottle_groups_for_assignment(
        self,
        assignment: SamplingTaskAssignment,
        task_detail: dict,
        current_user_id: int
    ) -> List[SamplingBottleGroup]:
        """
        为任务执行指派生成瓶组记录

        Args:
            assignment: 任务执行指派
            task_detail: 任务详情（包含周期条目）
            current_user_id: 当前用户ID

        Returns:
            创建的瓶组列表
        """
        bottle_groups_to_create = []
        detection_params_to_create = []

        # 获取过滤后的周期条目（排除现场直读类型）
        filtered_cycle_items = task_detail.get('filtered_cycle_items', [])

        # 按瓶组信息分组（相同瓶组只生成一条记录，默认瓶组单独生成）
        bottle_group_map = {}

        for cycle_item in filtered_cycle_items:
            # 获取技术手册信息
            technical_manual = cycle_item.get('technical_manual', {})
            bottle_maintenance_id = technical_manual.get('bottle_maintenance_id', 0)

            # 默认瓶组（bottle_maintenance_id为0）单独生成记录
            if bottle_maintenance_id == 0:
                group_key = f"default_{cycle_item.get('id')}"
            else:
                group_key = f"bottle_{bottle_maintenance_id}"

            if group_key not in bottle_group_map:
                bottle_group_map[group_key] = {
                    'bottle_maintenance_id': bottle_maintenance_id if bottle_maintenance_id != 0 else None,
                    'cycle_items': []
                }

            bottle_group_map[group_key]['cycle_items'].append(cycle_item)

        # 获取当前任务的最大瓶组序号
        current_max_sequence = await self.bottle_group_dao.get_max_bottle_group_sequence_by_task(
            assignment.sampling_task_id
        )

        # 获取任务信息生成瓶组编号
        from module_sampling.entity.do.sampling_task_do import SamplingTask
        task_stmt = select(SamplingTask).where(SamplingTask.id == assignment.sampling_task_id)
        task_result = await self.db.execute(task_stmt)
        task = task_result.scalar_one_or_none()
        task_code = task.task_code if task else f"TASK_{assignment.sampling_task_id}"

        # 生成瓶组记录
        sequence = current_max_sequence
        for group_key, group_info in bottle_group_map.items():
            sequence += 1
            bottle_group_code = f"{task_code}_BG{sequence:03d}"

            bottle_group = SamplingBottleGroup(
                sampling_task_id=assignment.sampling_task_id,
                task_assignment_id=assignment.id,
                bottle_maintenance_id=group_info['bottle_maintenance_id'],
                bottle_group_code=bottle_group_code,
                bottle_group_sequence=sequence,
                cycle_number=assignment.cycle_number,
                cycle_type=assignment.cycle_type,
                detection_category=assignment.detection_category,
                point_name=assignment.point_name,
                status=0,  # 待使用
                create_by=current_user_id
            )
            bottle_groups_to_create.append(bottle_group)

            # 为每个瓶组创建检测参数关联
            for cycle_item in group_info['cycle_items']:
                detection_param = SamplingBottleGroupDetectionParam(
                    bottle_group_id=None,  # 将在瓶组创建后设置
                    project_quotation_item_id=cycle_item.get('project_quotation_item_id'),
                    technical_manual_id=cycle_item.get('technical_manual', {}).get('id'),
                    qualification_code=cycle_item.get('qualification_code'),
                    classification=cycle_item.get('classification'),
                    category=cycle_item.get('category'),
                    parameter=cycle_item.get('parameter'),
                    method=cycle_item.get('method'),
                    limitation_scope=cycle_item.get('limitation_scope'),
                    sample_source=cycle_item.get('sample_source'),
                    point_name=cycle_item.get('point_name'),
                    point_count=cycle_item.get('point_count', 1),
                    cycle_type=cycle_item.get('cycle_type'),
                    cycle_count=cycle_item.get('cycle_count', 1),
                    frequency=cycle_item.get('frequency', 1),
                    sample_count=cycle_item.get('sample_count', 1),
                    create_by=current_user_id
                )
                detection_params_to_create.append((bottle_group, detection_param))

        # 批量创建瓶组
        if bottle_groups_to_create:
            created_bottle_groups = await self.bottle_group_dao.batch_create_bottle_groups(bottle_groups_to_create)

            # 创建检测参数关联
            params_to_create = []
            bottle_group_index = 0
            for bottle_group in created_bottle_groups:
                # 找到对应的检测参数
                for bottle_group_obj, detection_param in detection_params_to_create:
                    if bottle_group_obj.bottle_group_code == bottle_group.bottle_group_code:
                        detection_param.bottle_group_id = bottle_group.id
                        params_to_create.append(detection_param)

            if params_to_create:
                await self.bottle_group_dao.batch_create_detection_params(params_to_create)

            return created_bottle_groups

        return []
