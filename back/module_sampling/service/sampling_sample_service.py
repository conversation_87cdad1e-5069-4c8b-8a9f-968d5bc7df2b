"""
采样样品服务
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from module_sampling.dao.sampling_sample_dao import Sampling<PERSON>ampleDAO
from module_sampling.entity.do.sampling_sample_do import SamplingSample
from module_sampling.dto.sampling_sample_dto import (
    SamplingSampleCreateDTO,
    SamplingSampleUpdateDTO,
    SamplingSampleDTO,
    SamplingSampleQueryDTO
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from exceptions.exception import ServiceException
from utils.log_util import logger
from utils.page_util import PageUtil


class SamplingSampleService:
    """采样样品服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.sample_dao = SamplingSampleDAO(db)
    
    async def create_sample(self, create_dto: SamplingSampleCreateDTO, current_user: CurrentUserModel) -> SamplingSampleDTO:
        """创建样品"""
        try:
            sample = SamplingSample(
                sampling_task_id=create_dto.sampling_task_id,
                task_assignment_id=create_dto.task_assignment_id,
                sample_number=create_dto.sample_number,
                sample_code=create_dto.sample_code,
                cycle_number=create_dto.cycle_number,
                cycle_type=create_dto.cycle_type,
                detection_category=create_dto.detection_category,
                point_name=create_dto.point_name,
                sample_description=create_dto.sample_description,
                remark=create_dto.remark,
                create_by=current_user.user.user_id
            )
            
            created_sample = await self.sample_dao.create_sample(sample)
            await self.db.commit()
            
            return SamplingSampleDTO.model_validate(created_sample)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建样品失败: {str(e)}")
            raise ServiceException(f"创建样品失败: {str(e)}")
    
    async def update_sample(self, sample_id: int, update_dto: SamplingSampleUpdateDTO, current_user: CurrentUserModel) -> SamplingSampleDTO:
        """更新样品"""
        try:
            # 检查样品是否存在
            existing_sample = await self.sample_dao.get_sample_by_id(sample_id)
            if not existing_sample:
                raise ServiceException("样品不存在")
            
            # 准备更新数据
            update_data = update_dto.model_dump(exclude_unset=True)
            update_data['update_by'] = current_user.user.user_id
            
            updated_sample = await self.sample_dao.update_sample(sample_id, update_data)
            await self.db.commit()
            
            return SamplingSampleDTO.model_validate(updated_sample)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新样品失败: {str(e)}")
            raise ServiceException(f"更新样品失败: {str(e)}")
    
    async def get_sample_by_id(self, sample_id: int) -> SamplingSampleDTO:
        """根据ID获取样品"""
        sample = await self.sample_dao.get_sample_by_id(sample_id)
        if not sample:
            raise ServiceException("样品不存在")
        
        return SamplingSampleDTO.model_validate(sample)
    
    async def get_samples_by_task_id(self, task_id: int) -> List[SamplingSampleDTO]:
        """根据任务ID获取样品列表"""
        samples = await self.sample_dao.get_samples_by_task_id(task_id)
        return [SamplingSampleDTO.model_validate(sample) for sample in samples]
    
    async def get_samples_by_assignment_id(self, assignment_id: int) -> List[SamplingSampleDTO]:
        """根据任务执行指派ID获取样品列表"""
        samples = await self.sample_dao.get_samples_by_assignment_id(assignment_id)
        return [SamplingSampleDTO.model_validate(sample) for sample in samples]
    
    async def delete_sample(self, sample_id: int, current_user: CurrentUserModel) -> bool:
        """删除样品"""
        try:
            # 检查样品是否存在
            existing_sample = await self.sample_dao.get_sample_by_id(sample_id)
            if not existing_sample:
                raise ServiceException("样品不存在")
            
            # 检查样品状态，只有待采集状态的样品才能删除
            if existing_sample.status != 0:
                raise ServiceException("只有待采集状态的样品才能删除")
            
            result = await self.sample_dao.delete_sample(sample_id)
            await self.db.commit()
            
            return result
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除样品失败: {str(e)}")
            raise ServiceException(f"删除样品失败: {str(e)}")
    
    async def batch_update_sample_status(self, sample_ids: List[int], status: int, current_user: CurrentUserModel) -> int:
        """批量更新样品状态"""
        try:
            updated_count = 0
            for sample_id in sample_ids:
                update_data = {
                    'status': status,
                    'update_by': current_user.user.user_id
                }
                
                # 如果是设置为已采集状态，记录采集信息
                if status == 1:
                    from datetime import datetime
                    update_data['collection_date'] = datetime.now().date()
                    update_data['collection_time'] = datetime.now()
                    update_data['collector_id'] = current_user.user.user_id
                
                updated_sample = await self.sample_dao.update_sample(sample_id, update_data)
                if updated_sample:
                    updated_count += 1
            
            await self.db.commit()
            return updated_count
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"批量更新样品状态失败: {str(e)}")
            raise ServiceException(f"批量更新样品状态失败: {str(e)}")
    
    def get_sample_status_options(self) -> List[dict]:
        """获取样品状态选项"""
        return [
            {'label': '待采集', 'value': 0},
            {'label': '已采集', 'value': 1},
            {'label': '已送检', 'value': 2},
            {'label': '检测中', 'value': 3},
            {'label': '已完成', 'value': 4}
        ]
