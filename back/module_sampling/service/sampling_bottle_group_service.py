"""
采样瓶组服务
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from module_sampling.dao.sampling_bottle_group_dao import SamplingBottleGroupDAO
from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_sampling.entity.do.sampling_bottle_group_detection_param_do import SamplingBottleGroupDetectionParam
from module_sampling.dto.sampling_bottle_group_dto import (
    SamplingBottleGroupCreateDTO,
    SamplingBottleGroupUpdateDTO,
    SamplingBottleGroupDTO,
    SamplingBottleGroupQueryDTO,
    SamplingBottleGroupDetectionParamCreateDTO
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from exceptions.exception import ServiceException
from utils.log_util import logger


class SamplingBottleGroupService:
    """采样瓶组服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.bottle_group_dao = SamplingBottleGroupDAO(db)
    
    async def create_bottle_group(self, create_dto: SamplingBottleGroupCreateDTO, current_user: CurrentUserModel) -> SamplingBottleGroupDTO:
        """创建瓶组"""
        try:
            bottle_group = SamplingBottleGroup(
                sampling_task_id=create_dto.sampling_task_id,
                task_assignment_id=create_dto.task_assignment_id,
                bottle_maintenance_id=create_dto.bottle_maintenance_id,
                bottle_group_code=create_dto.bottle_group_code,
                bottle_group_sequence=create_dto.bottle_group_sequence,
                cycle_number=create_dto.cycle_number,
                cycle_type=create_dto.cycle_type,
                detection_category=create_dto.detection_category,
                point_name=create_dto.point_name,
                bottle_group_description=create_dto.bottle_group_description,
                remark=create_dto.remark,
                create_by=current_user.user.user_id
            )
            
            created_bottle_group = await self.bottle_group_dao.create_bottle_group(bottle_group)
            await self.db.commit()
            
            return SamplingBottleGroupDTO.model_validate(created_bottle_group)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建瓶组失败: {str(e)}")
            raise ServiceException(f"创建瓶组失败: {str(e)}")
    
    async def update_bottle_group(self, bottle_group_id: int, update_dto: SamplingBottleGroupUpdateDTO, current_user: CurrentUserModel) -> SamplingBottleGroupDTO:
        """更新瓶组"""
        try:
            # 检查瓶组是否存在
            existing_bottle_group = await self.bottle_group_dao.get_bottle_group_by_id(bottle_group_id)
            if not existing_bottle_group:
                raise ServiceException("瓶组不存在")
            
            # 准备更新数据
            update_data = update_dto.model_dump(exclude_unset=True)
            update_data['update_by'] = current_user.user.user_id
            
            updated_bottle_group = await self.bottle_group_dao.update_bottle_group(bottle_group_id, update_data)
            await self.db.commit()
            
            return SamplingBottleGroupDTO.model_validate(updated_bottle_group)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"更新瓶组失败: {str(e)}")
            raise ServiceException(f"更新瓶组失败: {str(e)}")
    
    async def get_bottle_group_by_id(self, bottle_group_id: int) -> SamplingBottleGroupDTO:
        """根据ID获取瓶组"""
        bottle_group = await self.bottle_group_dao.get_bottle_group_by_id(bottle_group_id)
        if not bottle_group:
            raise ServiceException("瓶组不存在")
        
        return SamplingBottleGroupDTO.model_validate(bottle_group)
    
    async def get_bottle_groups_by_task_id(self, task_id: int) -> List[dict]:
        """根据任务ID获取瓶组列表（包含检测参数）"""
        bottle_groups = await self.bottle_group_dao.get_bottle_groups_with_detection_params(task_id)
        
        result = []
        for bottle_group in bottle_groups:
            bottle_group_dict = SamplingBottleGroupDTO.model_validate(bottle_group).model_dump()
            
            # 获取检测参数
            detection_params = await self.bottle_group_dao.get_detection_params_by_bottle_group_id(bottle_group.id)
            bottle_group_dict['detection_params'] = [param.to_dict() for param in detection_params]
            
            # 获取瓶组信息
            if bottle_group.bottle_maintenance_id:
                from module_bottle_maintenance.service.bottle_maintenance_service import BottleMaintenanceService
                bottle_service = BottleMaintenanceService(self.db)
                try:
                    bottle_info = await bottle_service.get_bottle_maintenance_by_id(bottle_group.bottle_maintenance_id)
                    bottle_group_dict['bottle_info'] = bottle_info.model_dump()
                except:
                    bottle_group_dict['bottle_info'] = None
            else:
                bottle_group_dict['bottle_info'] = {
                    'bottle_code': 'DEFAULT',
                    'bottle_type': '默认瓶组',
                    'bottle_volume': '标准',
                    'storage_styles': [],
                    'fix_styles': []
                }
            
            result.append(bottle_group_dict)
        
        return result
    
    async def get_bottle_groups_by_assignment_id(self, assignment_id: int) -> List[dict]:
        """根据任务执行指派ID获取瓶组列表（包含检测参数）"""
        bottle_groups = await self.bottle_group_dao.get_bottle_groups_by_assignment_id(assignment_id)
        
        result = []
        for bottle_group in bottle_groups:
            bottle_group_dict = SamplingBottleGroupDTO.model_validate(bottle_group).model_dump()
            
            # 获取检测参数
            detection_params = await self.bottle_group_dao.get_detection_params_by_bottle_group_id(bottle_group.id)
            bottle_group_dict['detection_params'] = [param.to_dict() for param in detection_params]
            
            # 获取瓶组信息
            if bottle_group.bottle_maintenance_id:
                from module_bottle_maintenance.service.bottle_maintenance_service import BottleMaintenanceService
                bottle_service = BottleMaintenanceService(self.db)
                try:
                    bottle_info = await bottle_service.get_bottle_maintenance_by_id(bottle_group.bottle_maintenance_id)
                    bottle_group_dict['bottle_info'] = bottle_info.model_dump()
                except:
                    bottle_group_dict['bottle_info'] = None
            else:
                bottle_group_dict['bottle_info'] = {
                    'bottle_code': 'DEFAULT',
                    'bottle_type': '默认瓶组',
                    'bottle_volume': '标准',
                    'storage_styles': [],
                    'fix_styles': []
                }
            
            result.append(bottle_group_dict)
        
        return result
    
    async def delete_bottle_group(self, bottle_group_id: int, current_user: CurrentUserModel) -> bool:
        """删除瓶组"""
        try:
            # 检查瓶组是否存在
            existing_bottle_group = await self.bottle_group_dao.get_bottle_group_by_id(bottle_group_id)
            if not existing_bottle_group:
                raise ServiceException("瓶组不存在")
            
            # 检查瓶组状态，只有待使用状态的瓶组才能删除
            if existing_bottle_group.status != 0:
                raise ServiceException("只有待使用状态的瓶组才能删除")
            
            # 先删除检测参数关联
            await self.bottle_group_dao.delete_detection_params_by_bottle_group_id(bottle_group_id)
            
            # 删除瓶组
            result = await self.bottle_group_dao.delete_bottle_group(bottle_group_id)
            await self.db.commit()
            
            return result
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除瓶组失败: {str(e)}")
            raise ServiceException(f"删除瓶组失败: {str(e)}")
    
    async def batch_update_bottle_group_status(self, bottle_group_ids: List[int], status: int, current_user: CurrentUserModel) -> int:
        """批量更新瓶组状态"""
        try:
            updated_count = 0
            for bottle_group_id in bottle_group_ids:
                update_data = {
                    'status': status,
                    'update_by': current_user.user.user_id
                }
                
                updated_bottle_group = await self.bottle_group_dao.update_bottle_group(bottle_group_id, update_data)
                if updated_bottle_group:
                    updated_count += 1
            
            await self.db.commit()
            return updated_count
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"批量更新瓶组状态失败: {str(e)}")
            raise ServiceException(f"批量更新瓶组状态失败: {str(e)}")
    
    def get_bottle_group_status_options(self) -> List[dict]:
        """获取瓶组状态选项"""
        return [
            {'label': '待使用', 'value': 0},
            {'label': '使用中', 'value': 1},
            {'label': '已完成', 'value': 2}
        ]
