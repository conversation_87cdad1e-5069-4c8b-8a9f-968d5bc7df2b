-- 创建采样样品表
CREATE TABLE IF NOT EXISTS `sampling_sample` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `task_assignment_id` bigint NOT NULL COMMENT '任务执行指派ID',
  `sample_number` int NOT NULL COMMENT '样品序号（从1开始累加）',
  `sample_code` varchar(100) DEFAULT NULL COMMENT '样品编号（可选，用于标识具体样品）',
  `cycle_number` int NOT NULL COMMENT '周期序号',
  `cycle_type` varchar(50) DEFAULT NULL COMMENT '周期类型',
  `detection_category` varchar(100) DEFAULT NULL COMMENT '检测类别',
  `point_name` varchar(200) DEFAULT NULL COMMENT '点位名称',
  `status` int DEFAULT '0' COMMENT '样品状态：0-待采集，1-已采集，2-已送检，3-检测中，4-已完成',
  `collection_date` datetime DEFAULT NULL COMMENT '采集日期',
  `collection_time` datetime DEFAULT NULL COMMENT '采集时间',
  `collector_id` bigint DEFAULT NULL COMMENT '采集人ID',
  `collection_location` varchar(500) DEFAULT NULL COMMENT '采集地点详细描述',
  `sample_description` text COMMENT '样品描述',
  `remark` text COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sampling_sample_task_id` (`sampling_task_id`),
  KEY `idx_sampling_sample_assignment_id` (`task_assignment_id`),
  KEY `idx_sampling_sample_cycle` (`cycle_number`,`cycle_type`,`detection_category`,`point_name`),
  KEY `idx_sampling_sample_status` (`status`),
  KEY `fk_sampling_sample_task` (`sampling_task_id`),
  KEY `fk_sampling_sample_assignment` (`task_assignment_id`),
  KEY `fk_sampling_sample_collector` (`collector_id`),
  KEY `fk_sampling_sample_creator` (`create_by`),
  KEY `fk_sampling_sample_updater` (`update_by`),
  CONSTRAINT `fk_sampling_sample_task` FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sampling_sample_assignment` FOREIGN KEY (`task_assignment_id`) REFERENCES `sampling_task_executor_assignment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sampling_sample_collector` FOREIGN KEY (`collector_id`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_sampling_sample_creator` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_sampling_sample_updater` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采样样品表';

-- 创建采样瓶组表
CREATE TABLE IF NOT EXISTS `sampling_bottle_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `task_assignment_id` bigint NOT NULL COMMENT '任务执行指派ID',
  `bottle_maintenance_id` int DEFAULT NULL COMMENT '瓶组管理ID（NULL表示默认瓶组）',
  `bottle_group_code` varchar(100) NOT NULL COMMENT '瓶组编号（根据任务编号和瓶组序号生成）',
  `bottle_group_sequence` int NOT NULL COMMENT '瓶组序号（在同一任务中的序号）',
  `cycle_number` int NOT NULL COMMENT '周期序号',
  `cycle_type` varchar(50) DEFAULT NULL COMMENT '周期类型',
  `detection_category` varchar(100) DEFAULT NULL COMMENT '检测类别',
  `point_name` varchar(200) DEFAULT NULL COMMENT '点位名称',
  `status` int DEFAULT '0' COMMENT '瓶组状态：0-待使用，1-使用中，2-已完成',
  `bottle_group_description` text COMMENT '瓶组描述',
  `remark` text COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sampling_bottle_group_task_id` (`sampling_task_id`),
  KEY `idx_sampling_bottle_group_assignment_id` (`task_assignment_id`),
  KEY `idx_sampling_bottle_group_bottle_id` (`bottle_maintenance_id`),
  KEY `idx_sampling_bottle_group_cycle` (`cycle_number`,`cycle_type`,`detection_category`,`point_name`),
  KEY `idx_sampling_bottle_group_code` (`bottle_group_code`),
  KEY `fk_sampling_bottle_group_task` (`sampling_task_id`),
  KEY `fk_sampling_bottle_group_assignment` (`task_assignment_id`),
  KEY `fk_sampling_bottle_group_bottle` (`bottle_maintenance_id`),
  KEY `fk_sampling_bottle_group_creator` (`create_by`),
  KEY `fk_sampling_bottle_group_updater` (`update_by`),
  CONSTRAINT `fk_sampling_bottle_group_task` FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sampling_bottle_group_assignment` FOREIGN KEY (`task_assignment_id`) REFERENCES `sampling_task_executor_assignment` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sampling_bottle_group_bottle` FOREIGN KEY (`bottle_maintenance_id`) REFERENCES `bottle_maintenance` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_sampling_bottle_group_creator` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_sampling_bottle_group_updater` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采样瓶组表';

-- 创建采样瓶组检测参数关联表
CREATE TABLE IF NOT EXISTS `sampling_bottle_group_detection_param` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bottle_group_id` bigint NOT NULL COMMENT '采样瓶组ID',
  `project_quotation_item_id` bigint NOT NULL COMMENT '项目报价明细ID',
  `technical_manual_id` bigint DEFAULT NULL COMMENT '技术手册ID',
  `qualification_code` varchar(50) DEFAULT NULL COMMENT '资质唯一编号',
  `classification` varchar(50) DEFAULT NULL COMMENT '分类',
  `category` varchar(50) NOT NULL COMMENT '检测类别(二级分类)',
  `parameter` varchar(100) NOT NULL COMMENT '检测参数(指标)',
  `method` varchar(200) NOT NULL COMMENT '检测方法',
  `limitation_scope` varchar(200) DEFAULT NULL COMMENT '限制范围',
  `sample_source` varchar(50) DEFAULT NULL COMMENT '样品来源',
  `point_name` varchar(100) DEFAULT NULL COMMENT '点位名称',
  `point_count` int NOT NULL DEFAULT '1' COMMENT '点位数',
  `cycle_type` varchar(10) DEFAULT NULL COMMENT '检测周期类型',
  `cycle_count` int DEFAULT '1' COMMENT '检测周期数',
  `frequency` int DEFAULT '1' COMMENT '检测频次数',
  `sample_count` int DEFAULT '1' COMMENT '样品数',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_bottle_group_detection_bottle_id` (`bottle_group_id`),
  KEY `idx_bottle_group_detection_item_id` (`project_quotation_item_id`),
  KEY `idx_bottle_group_detection_manual_id` (`technical_manual_id`),
  KEY `idx_bottle_group_detection_category` (`category`,`parameter`,`method`),
  KEY `fk_bottle_group_detection_bottle` (`bottle_group_id`),
  KEY `fk_bottle_group_detection_item` (`project_quotation_item_id`),
  KEY `fk_bottle_group_detection_manual` (`technical_manual_id`),
  KEY `fk_bottle_group_detection_creator` (`create_by`),
  KEY `fk_bottle_group_detection_updater` (`update_by`),
  CONSTRAINT `fk_bottle_group_detection_bottle` FOREIGN KEY (`bottle_group_id`) REFERENCES `sampling_bottle_group` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_bottle_group_detection_item` FOREIGN KEY (`project_quotation_item_id`) REFERENCES `project_quotation_item` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_bottle_group_detection_manual` FOREIGN KEY (`technical_manual_id`) REFERENCES `technical_manual` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_bottle_group_detection_creator` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_bottle_group_detection_updater` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采样瓶组检测参数关联表';

-- 添加样品状态字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) 
VALUES ('样品状态', 'sampling_sample_status', '0', 'admin', NOW(), '样品状态列表') 
ON DUPLICATE KEY UPDATE `dict_name` = VALUES(`dict_name`);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待采集', '0', 'sampling_sample_status', '', 'warning', 'Y', '0', 'admin', NOW(), '样品待采集状态'),
(2, '已采集', '1', 'sampling_sample_status', '', 'primary', 'N', '0', 'admin', NOW(), '样品已采集状态'),
(3, '已送检', '2', 'sampling_sample_status', '', 'info', 'N', '0', 'admin', NOW(), '样品已送检状态'),
(4, '检测中', '3', 'sampling_sample_status', '', 'success', 'N', '0', 'admin', NOW(), '样品检测中状态'),
(5, '已完成', '4', 'sampling_sample_status', '', 'success', 'N', '0', 'admin', NOW(), '样品已完成状态')
ON DUPLICATE KEY UPDATE `dict_label` = VALUES(`dict_label`);

-- 添加瓶组状态字典数据
INSERT INTO `sys_dict_type` (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `remark`) 
VALUES ('瓶组状态', 'sampling_bottle_group_status', '0', 'admin', NOW(), '瓶组状态列表') 
ON DUPLICATE KEY UPDATE `dict_name` = VALUES(`dict_name`);

INSERT INTO `sys_dict_data` (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `remark`) VALUES
(1, '待使用', '0', 'sampling_bottle_group_status', '', 'warning', 'Y', '0', 'admin', NOW(), '瓶组待使用状态'),
(2, '使用中', '1', 'sampling_bottle_group_status', '', 'primary', 'N', '0', 'admin', NOW(), '瓶组使用中状态'),
(3, '已完成', '2', 'sampling_bottle_group_status', '', 'success', 'N', '0', 'admin', NOW(), '瓶组已完成状态')
ON DUPLICATE KEY UPDATE `dict_label` = VALUES(`dict_label`);
