"""
采样瓶组服务单元测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime

from module_sampling.service.sampling_bottle_group_service import SamplingBottleGroupService
from module_sampling.dto.sampling_bottle_group_dto import (
    SamplingBottleGroupCreateDTO,
    SamplingBottleGroupUpdateDTO
)
from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_sampling.entity.do.sampling_bottle_group_detection_param_do import SamplingBottleGroupDetectionParam
from module_admin.entity.vo.user_vo import CurrentUserModel, SysUserModel
from exceptions.exception import ServiceException


@pytest.fixture
def mock_db():
    """模拟数据库会话"""
    return AsyncMock()


@pytest.fixture
def mock_bottle_group_dao():
    """模拟瓶组DAO"""
    return AsyncMock()


@pytest.fixture
def current_user():
    """模拟当前用户"""
    user = SysUserModel(
        user_id=1,
        user_name="test_user",
        nick_name="测试用户"
    )
    return CurrentUserModel(user=user)


@pytest.fixture
def bottle_group_service(mock_db, mock_bottle_group_dao):
    """创建瓶组服务实例"""
    service = SamplingBottleGroupService(mock_db)
    service.bottle_group_dao = mock_bottle_group_dao
    return service


@pytest.fixture
def bottle_group_create_dto():
    """瓶组创建DTO"""
    return SamplingBottleGroupCreateDTO(
        sampling_task_id=1,
        task_assignment_id=1,
        bottle_maintenance_id=1,
        bottle_group_code="TASK001_BG001",
        bottle_group_sequence=1,
        cycle_number=1,
        cycle_type="月度",
        detection_category="水质",
        point_name="监测点1",
        bottle_group_description="测试瓶组",
        remark="测试备注"
    )


@pytest.fixture
def bottle_group_entity():
    """瓶组实体"""
    return SamplingBottleGroup(
        id=1,
        sampling_task_id=1,
        task_assignment_id=1,
        bottle_maintenance_id=1,
        bottle_group_code="TASK001_BG001",
        bottle_group_sequence=1,
        cycle_number=1,
        cycle_type="月度",
        detection_category="水质",
        point_name="监测点1",
        status=0,
        bottle_group_description="测试瓶组",
        remark="测试备注",
        create_by=1,
        create_time=datetime.now()
    )


@pytest.fixture
def detection_param_entity():
    """检测参数实体"""
    return SamplingBottleGroupDetectionParam(
        id=1,
        bottle_group_id=1,
        project_quotation_item_id=1,
        technical_manual_id=1,
        category="水质",
        parameter="pH值",
        method="玻璃电极法",
        sample_count=1,
        create_by=1
    )


class TestSamplingBottleGroupService:
    """采样瓶组服务测试类"""

    @pytest.mark.asyncio
    async def test_create_bottle_group_success(self, bottle_group_service, bottle_group_create_dto, bottle_group_entity, current_user):
        """测试创建瓶组成功"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.create_bottle_group.return_value = bottle_group_entity
        
        # 调用服务方法
        result = await bottle_group_service.create_bottle_group(bottle_group_create_dto, current_user)
        
        # 验证结果
        assert result.id == 1
        assert result.bottle_group_code == "TASK001_BG001"
        assert result.sampling_task_id == 1
        
        # 验证DAO方法被调用
        bottle_group_service.bottle_group_dao.create_bottle_group.assert_called_once()
        bottle_group_service.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_bottle_group_failure(self, bottle_group_service, bottle_group_create_dto, current_user):
        """测试创建瓶组失败"""
        # 设置模拟异常
        bottle_group_service.bottle_group_dao.create_bottle_group.side_effect = Exception("数据库错误")
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await bottle_group_service.create_bottle_group(bottle_group_create_dto, current_user)
        
        assert "创建瓶组失败" in str(exc_info.value)
        bottle_group_service.db.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_bottle_group_success(self, bottle_group_service, bottle_group_entity, current_user):
        """测试更新瓶组成功"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.get_bottle_group_by_id.return_value = bottle_group_entity
        bottle_group_service.bottle_group_dao.update_bottle_group.return_value = bottle_group_entity
        
        update_dto = SamplingBottleGroupUpdateDTO(
            bottle_group_description="更新后的瓶组描述"
        )
        
        # 调用服务方法
        result = await bottle_group_service.update_bottle_group(1, update_dto, current_user)
        
        # 验证结果
        assert result.id == 1
        
        # 验证DAO方法被调用
        bottle_group_service.bottle_group_dao.get_bottle_group_by_id.assert_called_once_with(1)
        bottle_group_service.bottle_group_dao.update_bottle_group.assert_called_once()
        bottle_group_service.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_bottle_group_not_found(self, bottle_group_service, current_user):
        """测试更新不存在的瓶组"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.get_bottle_group_by_id.return_value = None
        
        update_dto = SamplingBottleGroupUpdateDTO(bottle_group_description="更新描述")
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await bottle_group_service.update_bottle_group(999, update_dto, current_user)
        
        assert "瓶组不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_bottle_group_by_id_success(self, bottle_group_service, bottle_group_entity):
        """测试根据ID获取瓶组成功"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.get_bottle_group_by_id.return_value = bottle_group_entity
        
        # 调用服务方法
        result = await bottle_group_service.get_bottle_group_by_id(1)
        
        # 验证结果
        assert result.id == 1
        assert result.bottle_group_code == "TASK001_BG001"

    @pytest.mark.asyncio
    async def test_get_bottle_group_by_id_not_found(self, bottle_group_service):
        """测试获取不存在的瓶组"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.get_bottle_group_by_id.return_value = None
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await bottle_group_service.get_bottle_group_by_id(999)
        
        assert "瓶组不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_bottle_groups_by_task_id(self, bottle_group_service, bottle_group_entity, detection_param_entity):
        """测试根据任务ID获取瓶组列表"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.get_bottle_groups_with_detection_params.return_value = [bottle_group_entity]
        bottle_group_service.bottle_group_dao.get_detection_params_by_bottle_group_id.return_value = [detection_param_entity]
        
        # 调用服务方法
        result = await bottle_group_service.get_bottle_groups_by_task_id(1)
        
        # 验证结果
        assert len(result) == 1
        assert result[0]['id'] == 1
        assert 'detection_params' in result[0]
        assert len(result[0]['detection_params']) == 1

    @pytest.mark.asyncio
    async def test_delete_bottle_group_success(self, bottle_group_service, bottle_group_entity, current_user):
        """测试删除瓶组成功"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.get_bottle_group_by_id.return_value = bottle_group_entity
        bottle_group_service.bottle_group_dao.delete_detection_params_by_bottle_group_id.return_value = 1
        bottle_group_service.bottle_group_dao.delete_bottle_group.return_value = True
        
        # 调用服务方法
        result = await bottle_group_service.delete_bottle_group(1, current_user)
        
        # 验证结果
        assert result is True
        bottle_group_service.bottle_group_dao.delete_detection_params_by_bottle_group_id.assert_called_once_with(1)
        bottle_group_service.bottle_group_dao.delete_bottle_group.assert_called_once_with(1)
        bottle_group_service.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_bottle_group_wrong_status(self, bottle_group_service, current_user):
        """测试删除状态不正确的瓶组"""
        # 创建状态为使用中的瓶组
        bottle_group_entity = SamplingBottleGroup(
            id=1,
            sampling_task_id=1,
            task_assignment_id=1,
            bottle_group_code="TASK001_BG001",
            bottle_group_sequence=1,
            cycle_number=1,
            status=1,  # 使用中状态
            create_by=1
        )
        
        bottle_group_service.bottle_group_dao.get_bottle_group_by_id.return_value = bottle_group_entity
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await bottle_group_service.delete_bottle_group(1, current_user)
        
        assert "只有待使用状态的瓶组才能删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_batch_update_bottle_group_status(self, bottle_group_service, current_user):
        """测试批量更新瓶组状态"""
        # 设置模拟返回值
        bottle_group_service.bottle_group_dao.update_bottle_group.return_value = MagicMock()
        
        # 调用服务方法
        result = await bottle_group_service.batch_update_bottle_group_status([1, 2, 3], 1, current_user)
        
        # 验证结果
        assert result == 3
        assert bottle_group_service.bottle_group_dao.update_bottle_group.call_count == 3
        bottle_group_service.db.commit.assert_called_once()

    def test_get_bottle_group_status_options(self, bottle_group_service):
        """测试获取瓶组状态选项"""
        options = bottle_group_service.get_bottle_group_status_options()
        
        assert len(options) == 3
        assert options[0]['label'] == '待使用'
        assert options[0]['value'] == 0
