"""
采样样品服务单元测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime

from module_sampling.service.sampling_sample_service import SamplingSampleService
from module_sampling.dto.sampling_sample_dto import (
    SamplingSampleCreateDTO,
    SamplingSampleUpdateDTO
)
from module_sampling.entity.do.sampling_sample_do import SamplingSample
from module_admin.entity.vo.user_vo import CurrentUserModel, SysUserModel
from exceptions.exception import ServiceException


@pytest.fixture
def mock_db():
    """模拟数据库会话"""
    return AsyncMock()


@pytest.fixture
def mock_sample_dao():
    """模拟样品DAO"""
    return AsyncMock()


@pytest.fixture
def current_user():
    """模拟当前用户"""
    user = SysUserModel(
        user_id=1,
        user_name="test_user",
        nick_name="测试用户"
    )
    return CurrentUserModel(user=user)


@pytest.fixture
def sample_service(mock_db, mock_sample_dao):
    """创建样品服务实例"""
    service = SamplingSampleService(mock_db)
    service.sample_dao = mock_sample_dao
    return service


@pytest.fixture
def sample_create_dto():
    """样品创建DTO"""
    return SamplingSampleCreateDTO(
        sampling_task_id=1,
        task_assignment_id=1,
        sample_number=1,
        sample_code="SAMPLE001",
        cycle_number=1,
        cycle_type="月度",
        detection_category="水质",
        point_name="监测点1",
        sample_description="测试样品",
        remark="测试备注"
    )


@pytest.fixture
def sample_entity():
    """样品实体"""
    return SamplingSample(
        id=1,
        sampling_task_id=1,
        task_assignment_id=1,
        sample_number=1,
        sample_code="SAMPLE001",
        cycle_number=1,
        cycle_type="月度",
        detection_category="水质",
        point_name="监测点1",
        status=0,
        sample_description="测试样品",
        remark="测试备注",
        create_by=1,
        create_time=datetime.now()
    )


class TestSamplingSampleService:
    """采样样品服务测试类"""

    @pytest.mark.asyncio
    async def test_create_sample_success(self, sample_service, sample_create_dto, sample_entity, current_user):
        """测试创建样品成功"""
        # 设置模拟返回值
        sample_service.sample_dao.create_sample.return_value = sample_entity
        
        # 调用服务方法
        result = await sample_service.create_sample(sample_create_dto, current_user)
        
        # 验证结果
        assert result.id == 1
        assert result.sample_code == "SAMPLE001"
        assert result.sampling_task_id == 1
        
        # 验证DAO方法被调用
        sample_service.sample_dao.create_sample.assert_called_once()
        sample_service.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_sample_failure(self, sample_service, sample_create_dto, current_user):
        """测试创建样品失败"""
        # 设置模拟异常
        sample_service.sample_dao.create_sample.side_effect = Exception("数据库错误")
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await sample_service.create_sample(sample_create_dto, current_user)
        
        assert "创建样品失败" in str(exc_info.value)
        sample_service.db.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_sample_success(self, sample_service, sample_entity, current_user):
        """测试更新样品成功"""
        # 设置模拟返回值
        sample_service.sample_dao.get_sample_by_id.return_value = sample_entity
        sample_service.sample_dao.update_sample.return_value = sample_entity
        
        update_dto = SamplingSampleUpdateDTO(
            sample_code="SAMPLE002",
            sample_description="更新后的样品描述"
        )
        
        # 调用服务方法
        result = await sample_service.update_sample(1, update_dto, current_user)
        
        # 验证结果
        assert result.id == 1
        
        # 验证DAO方法被调用
        sample_service.sample_dao.get_sample_by_id.assert_called_once_with(1)
        sample_service.sample_dao.update_sample.assert_called_once()
        sample_service.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_sample_not_found(self, sample_service, current_user):
        """测试更新不存在的样品"""
        # 设置模拟返回值
        sample_service.sample_dao.get_sample_by_id.return_value = None
        
        update_dto = SamplingSampleUpdateDTO(sample_code="SAMPLE002")
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await sample_service.update_sample(999, update_dto, current_user)
        
        assert "样品不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_sample_by_id_success(self, sample_service, sample_entity):
        """测试根据ID获取样品成功"""
        # 设置模拟返回值
        sample_service.sample_dao.get_sample_by_id.return_value = sample_entity
        
        # 调用服务方法
        result = await sample_service.get_sample_by_id(1)
        
        # 验证结果
        assert result.id == 1
        assert result.sample_code == "SAMPLE001"

    @pytest.mark.asyncio
    async def test_get_sample_by_id_not_found(self, sample_service):
        """测试获取不存在的样品"""
        # 设置模拟返回值
        sample_service.sample_dao.get_sample_by_id.return_value = None
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await sample_service.get_sample_by_id(999)
        
        assert "样品不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_samples_by_task_id(self, sample_service, sample_entity):
        """测试根据任务ID获取样品列表"""
        # 设置模拟返回值
        sample_service.sample_dao.get_samples_by_task_id.return_value = [sample_entity]
        
        # 调用服务方法
        result = await sample_service.get_samples_by_task_id(1)
        
        # 验证结果
        assert len(result) == 1
        assert result[0].id == 1

    @pytest.mark.asyncio
    async def test_delete_sample_success(self, sample_service, sample_entity, current_user):
        """测试删除样品成功"""
        # 设置模拟返回值
        sample_service.sample_dao.get_sample_by_id.return_value = sample_entity
        sample_service.sample_dao.delete_sample.return_value = True
        
        # 调用服务方法
        result = await sample_service.delete_sample(1, current_user)
        
        # 验证结果
        assert result is True
        sample_service.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_sample_wrong_status(self, sample_service, current_user):
        """测试删除状态不正确的样品"""
        # 创建状态为已采集的样品
        sample_entity = SamplingSample(
            id=1,
            sampling_task_id=1,
            task_assignment_id=1,
            sample_number=1,
            status=1,  # 已采集状态
            create_by=1
        )
        
        sample_service.sample_dao.get_sample_by_id.return_value = sample_entity
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await sample_service.delete_sample(1, current_user)
        
        assert "只有待采集状态的样品才能删除" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_batch_update_sample_status(self, sample_service, current_user):
        """测试批量更新样品状态"""
        # 设置模拟返回值
        sample_service.sample_dao.update_sample.return_value = MagicMock()
        
        # 调用服务方法
        result = await sample_service.batch_update_sample_status([1, 2, 3], 1, current_user)
        
        # 验证结果
        assert result == 3
        assert sample_service.sample_dao.update_sample.call_count == 3
        sample_service.db.commit.assert_called_once()

    def test_get_sample_status_options(self, sample_service):
        """测试获取样品状态选项"""
        options = sample_service.get_sample_status_options()
        
        assert len(options) == 5
        assert options[0]['label'] == '待采集'
        assert options[0]['value'] == 0
