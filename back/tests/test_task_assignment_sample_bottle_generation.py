"""
任务执行指派样品和瓶组生成功能单元测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from module_sampling.service.task_assignment_service import TaskAssignmentService
from module_sampling.entity.do.sampling_task_assignment_do import SamplingTaskAssignment
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_sampling.entity.do.sampling_sample_do import SamplingSample
from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from exceptions.exception import ServiceException


@pytest.fixture
def mock_db():
    """模拟数据库会话"""
    return AsyncMock()


@pytest.fixture
def mock_assignment_dao():
    """模拟任务执行指派DAO"""
    return AsyncMock()


@pytest.fixture
def mock_sample_dao():
    """模拟样品DAO"""
    return AsyncMock()


@pytest.fixture
def mock_bottle_group_dao():
    """模拟瓶组DAO"""
    return AsyncMock()


@pytest.fixture
def task_assignment_service(mock_db, mock_assignment_dao, mock_sample_dao, mock_bottle_group_dao):
    """创建任务执行指派服务实例"""
    service = TaskAssignmentService(mock_db)
    service.assignment_dao = mock_assignment_dao
    service.sample_dao = mock_sample_dao
    service.bottle_group_dao = mock_bottle_group_dao
    return service


@pytest.fixture
def assignment_entity():
    """任务执行指派实体"""
    return SamplingTaskAssignment(
        id=1,
        sampling_task_id=1,
        cycle_number=1,
        cycle_type="月度",
        detection_category="水质",
        point_name="监测点1",
        status=0,
        create_by=1
    )


@pytest.fixture
def task_detail():
    """任务详情数据"""
    return {
        'id': 1,
        'sampling_task_id': 1,
        'cycle_number': 1,
        'cycle_type': '月度',
        'detection_category': '水质',
        'point_name': '监测点1',
        'filtered_cycle_items': [
            {
                'id': 1,
                'project_quotation_item_id': 1,
                'qualification_code': 'TEST001',
                'classification': '水质',
                'category': '水质',
                'parameter': 'pH值',
                'method': '玻璃电极法',
                'limitation_scope': '6.5-8.5',
                'sample_source': '地表水',
                'point_name': '监测点1',
                'point_count': 1,
                'cycle_type': '月度',
                'cycle_count': 1,
                'frequency': 1,
                'sample_count': 2,
                'technical_manual': {
                    'id': 1,
                    'bottle_maintenance_id': 1
                }
            },
            {
                'id': 2,
                'project_quotation_item_id': 2,
                'qualification_code': 'TEST002',
                'classification': '水质',
                'category': '水质',
                'parameter': '溶解氧',
                'method': '电化学探头法',
                'limitation_scope': '>5mg/L',
                'sample_source': '地表水',
                'point_name': '监测点1',
                'point_count': 1,
                'cycle_type': '月度',
                'cycle_count': 1,
                'frequency': 1,
                'sample_count': 1,
                'technical_manual': {
                    'id': 2,
                    'bottle_maintenance_id': 0  # 默认瓶组
                }
            }
        ]
    }


class TestTaskAssignmentSampleBottleGeneration:
    """任务执行指派样品和瓶组生成测试类"""

    @pytest.mark.asyncio
    async def test_generate_samples_and_bottle_groups_success(
        self, 
        task_assignment_service, 
        assignment_entity, 
        task_detail
    ):
        """测试生成样品和瓶组成功"""
        # 设置模拟返回值
        task_assignment_service.assignment_dao.get_assignment_by_id.return_value = assignment_entity
        
        # 模拟get_execution_task_detail方法
        with patch.object(task_assignment_service, 'get_execution_task_detail', return_value=task_detail):
            # 模拟_generate_samples_for_assignment方法
            sample_entities = [
                SamplingSample(id=1, sample_number=1, sampling_task_id=1, task_assignment_id=1),
                SamplingSample(id=2, sample_number=2, sampling_task_id=1, task_assignment_id=1)
            ]
            with patch.object(task_assignment_service, '_generate_samples_for_assignment', return_value=sample_entities):
                # 模拟_generate_bottle_groups_for_assignment方法
                bottle_group_entities = [
                    SamplingBottleGroup(id=1, bottle_group_code="TASK001_BG001", sampling_task_id=1, task_assignment_id=1),
                    SamplingBottleGroup(id=2, bottle_group_code="TASK001_BG002", sampling_task_id=1, task_assignment_id=1)
                ]
                with patch.object(task_assignment_service, '_generate_bottle_groups_for_assignment', return_value=bottle_group_entities):
                    # 调用服务方法
                    result = await task_assignment_service.generate_samples_and_bottle_groups(1, 1)
                    
                    # 验证结果
                    assert result['assignment_id'] == 1
                    assert result['samples_created'] == 2
                    assert result['bottle_groups_created'] == 2
                    assert len(result['samples']) == 2
                    assert len(result['bottle_groups']) == 2
                    
                    # 验证数据库提交
                    task_assignment_service.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_samples_and_bottle_groups_assignment_not_found(self, task_assignment_service):
        """测试任务执行指派不存在"""
        # 设置模拟返回值
        task_assignment_service.assignment_dao.get_assignment_by_id.return_value = None
        
        # 验证抛出异常
        with pytest.raises(ServiceException) as exc_info:
            await task_assignment_service.generate_samples_and_bottle_groups(999, 1)
        
        assert "执行任务不存在" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_samples_and_bottle_groups_failure(
        self, 
        task_assignment_service, 
        assignment_entity
    ):
        """测试生成样品和瓶组失败"""
        # 设置模拟返回值
        task_assignment_service.assignment_dao.get_assignment_by_id.return_value = assignment_entity
        
        # 模拟get_execution_task_detail方法抛出异常
        with patch.object(task_assignment_service, 'get_execution_task_detail', side_effect=Exception("获取任务详情失败")):
            # 验证抛出异常
            with pytest.raises(ServiceException) as exc_info:
                await task_assignment_service.generate_samples_and_bottle_groups(1, 1)
            
            assert "生成样品和瓶组失败" in str(exc_info.value)
            task_assignment_service.db.rollback.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_samples_for_assignment(
        self, 
        task_assignment_service, 
        assignment_entity, 
        task_detail
    ):
        """测试为任务执行指派生成样品记录"""
        # 设置模拟返回值
        task_assignment_service.sample_dao.get_max_sample_number_by_cycle.return_value = 0
        
        sample_entities = [
            SamplingSample(id=1, sample_number=1),
            SamplingSample(id=2, sample_number=2)
        ]
        task_assignment_service.sample_dao.batch_create_samples.return_value = sample_entities
        
        # 调用私有方法
        result = await task_assignment_service._generate_samples_for_assignment(
            assignment_entity, task_detail, 1
        )
        
        # 验证结果
        assert len(result) == 2
        assert result[0].sample_number == 1
        assert result[1].sample_number == 2
        
        # 验证DAO方法被调用
        task_assignment_service.sample_dao.get_max_sample_number_by_cycle.assert_called_once()
        task_assignment_service.sample_dao.batch_create_samples.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_bottle_groups_for_assignment(
        self, 
        task_assignment_service, 
        assignment_entity, 
        task_detail
    ):
        """测试为任务执行指派生成瓶组记录"""
        # 设置模拟返回值
        task_assignment_service.bottle_group_dao.get_max_bottle_group_sequence_by_task.return_value = 0
        
        # 模拟数据库查询任务信息
        task_entity = SamplingTask(id=1, task_code="TASK001")
        with patch.object(task_assignment_service.db, 'execute') as mock_execute:
            mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = task_entity
            mock_execute.return_value = mock_result
            
            bottle_group_entities = [
                SamplingBottleGroup(id=1, bottle_group_code="TASK001_BG001"),
                SamplingBottleGroup(id=2, bottle_group_code="TASK001_BG002")
            ]
            task_assignment_service.bottle_group_dao.batch_create_bottle_groups.return_value = bottle_group_entities
            task_assignment_service.bottle_group_dao.batch_create_detection_params.return_value = []
            
            # 调用私有方法
            result = await task_assignment_service._generate_bottle_groups_for_assignment(
                assignment_entity, task_detail, 1
            )
            
            # 验证结果
            assert len(result) == 2
            
            # 验证DAO方法被调用
            task_assignment_service.bottle_group_dao.get_max_bottle_group_sequence_by_task.assert_called_once()
            task_assignment_service.bottle_group_dao.batch_create_bottle_groups.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_samples_for_assignment_no_filtered_items(
        self, 
        task_assignment_service, 
        assignment_entity
    ):
        """测试没有过滤后的周期条目时生成样品"""
        task_detail_empty = {
            'filtered_cycle_items': []
        }
        
        # 调用私有方法
        result = await task_assignment_service._generate_samples_for_assignment(
            assignment_entity, task_detail_empty, 1
        )
        
        # 验证结果
        assert len(result) == 0

    @pytest.mark.asyncio
    async def test_generate_bottle_groups_for_assignment_no_filtered_items(
        self, 
        task_assignment_service, 
        assignment_entity
    ):
        """测试没有过滤后的周期条目时生成瓶组"""
        task_detail_empty = {
            'filtered_cycle_items': []
        }
        
        # 调用私有方法
        result = await task_assignment_service._generate_bottle_groups_for_assignment(
            assignment_entity, task_detail_empty, 1
        )
        
        # 验证结果
        assert len(result) == 0
